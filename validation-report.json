{"summary": {"totalChecks": 77, "passed": 67, "failed": 10, "successRate": "87.0", "duration": 0.002, "timestamp": "2025-08-06T00:27:16.165Z"}, "details": [{"status": "PASS", "message": "IS3StorageService interface file exists"}, {"status": "PASS", "message": "S3StorageService implementation file exists"}, {"status": "PASS", "message": "UpdateObjectMetadataAsync method in interface"}, {"status": "PASS", "message": "GetS3ConfigurationAsync method in interface"}, {"status": "PASS", "message": "SearchByMetadataAsync method in interface"}, {"status": "PASS", "message": "S3ObjectWithMetadata class defined"}, {"status": "PASS", "message": "UpdateObjectMetadataAsync method implemented"}, {"status": "PASS", "message": "S3 metadata update using copy operation"}, {"status": "PASS", "message": "S3 configuration retrieval implemented"}, {"status": "PASS", "message": "Metadata search functionality implemented"}, {"status": "PASS", "message": "S3MetadataController exists"}, {"status": "PASS", "message": "AdminController exists"}, {"status": "PASS", "message": "Metadata field CatName present in controller"}, {"status": "PASS", "message": "Metadata field CatId present in controller"}, {"status": "PASS", "message": "Metadata field RegisteredName present in controller"}, {"status": "PASS", "message": "Metadata field FatherId present in controller"}, {"status": "PASS", "message": "Metadata field MotherId present in controller"}, {"status": "PASS", "message": "Metadata field BreedingStatus present in controller"}, {"status": "PASS", "message": "Metadata field AvailabilityStatus present in controller"}, {"status": "PASS", "message": "Metadata field PhotoType present in controller"}, {"status": "PASS", "message": "Metadata field Tags present in controller"}, {"status": "PASS", "message": "Metadata field Champion<PERSON><PERSON><PERSON> present in controller"}, {"status": "PASS", "message": "Metadata field GenerationLevel present in controller"}, {"status": "PASS", "message": "Bulk update endpoint present"}, {"status": "PASS", "message": "Admin authorization required"}, {"status": "PASS", "message": "S3 config endpoint present"}, {"status": "PASS", "message": "List all cats endpoint present"}, {"status": "PASS", "message": "Unlinked photos endpoint present"}, {"status": "PASS", "message": "Cat search endpoint present"}, {"status": "PASS", "message": "Search request model present"}, {"status": "PASS", "message": "Admin metadata editor HTML exists"}, {"status": "PASS", "message": "Admin JavaScript file exists"}, {"status": "PASS", "message": "overview tab panel present"}, {"status": "PASS", "message": "cats tab panel present"}, {"status": "PASS", "message": "photos tab panel present"}, {"status": "PASS", "message": "bulk tab panel present"}, {"status": "PASS", "message": "pedigree tab panel present"}, {"status": "PASS", "message": "Bulk cat name field present"}, {"status": "PASS", "message": "Bulk bloodline field present"}, {"status": "PASS", "message": "Apply bulk metadata function present"}, {"status": "PASS", "message": "Photo selection functionality present"}, {"status": "PASS", "message": "Load unlinked photos function present"}, {"status": "PASS", "message": "Litter management present"}, {"status": "PASS", "message": "Pedigree builder present"}, {"status": "PASS", "message": "Maine Coon breed specific"}, {"status": "PASS", "message": "Bloodline management"}, {"status": "PASS", "message": "Champion title tracking"}, {"status": "PASS", "message": "Pedigree functionality"}, {"status": "PASS", "message": "Link to metadata editor"}, {"status": "PASS", "message": "Quick stats functionality"}, {"status": "PASS", "message": "Cat statistics tracking"}, {"status": "PASS", "message": "Breeding status tracking"}, {"status": "PASS", "message": "Breeding field bloodline implemented"}, {"status": "FAIL", "message": "Breeding field champion-titles implemented"}, {"status": "FAIL", "message": "Breeding field generation-level implemented"}, {"status": "FAIL", "message": "Breeding field father-id implemented"}, {"status": "FAIL", "message": "Breeding field mother-id implemented"}, {"status": "FAIL", "message": "Breeding field breeding-status implemented"}, {"status": "FAIL", "message": "Breeding field availability-status implemented"}, {"status": "FAIL", "message": "Breeding field registration-number implemented"}, {"status": "FAIL", "message": "Photo field photo-type implemented"}, {"status": "FAIL", "message": "Photo field age-at-photo implemented"}, {"status": "PASS", "message": "Photo field tags implemented"}, {"status": "PASS", "message": "System tracking: updated-by"}, {"status": "PASS", "message": "System tracking: updated-at"}, {"status": "FAIL", "message": "Correct API base URL"}, {"status": "PASS", "message": "JWT token authentication"}, {"status": "PASS", "message": "Token storage handling"}, {"status": "PASS", "message": "AWS S3 integration"}, {"status": "PASS", "message": "Metadata handling"}, {"status": "PASS", "message": "Metadata update mechanism"}, {"status": "PASS", "message": "Admin authorization attribute present"}, {"status": "PASS", "message": "SuperAdmin role checking"}, {"status": "PASS", "message": "Admin role checking"}, {"status": "PASS", "message": "Editor role checking"}, {"status": "PASS", "message": "Authorization required"}, {"status": "PASS", "message": "Admin role required"}]}