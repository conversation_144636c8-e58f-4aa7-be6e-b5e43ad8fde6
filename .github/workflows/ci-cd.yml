name: CI/CD Pipeline

on:
  push:
    branches:
      - main
      - master
      - develop
      - 'release/*'
      - 'feature/*'
      - 'fix/*'
  pull_request:
    branches:
      - main
      - master
      - develop
  workflow_dispatch:
    inputs:
      force_rebuild:
        description: 'Force rebuild without cache'
        required: false
        default: false
        type: boolean
      deploy_to_production:
        description: 'Deploy to production after build'
        required: false
        default: false
        type: boolean

env:
  AWS_REGION: ap-southeast-2
  AWS_ACCOUNT_ID: ************
  ECR_REGISTRY: ************.dkr.ecr.ap-southeast-2.amazonaws.com

jobs:
  build-and-push:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    outputs:
      git_sha: ${{ steps.git_info.outputs.sha }}
      git_sha_short: ${{ steps.git_info.outputs.sha_short }}
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get Git Information
        id: git_info
        run: |
          echo "sha=$(git rev-parse HEAD)" >> $GITHUB_OUTPUT
          echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
          echo "branch=${GITHUB_REF#refs/heads/}" >> $GITHUB_OUTPUT
          echo "timestamp=$(git show -s --format=%cI HEAD)" >> $GITHUB_OUTPUT

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Create Build Info
        run: |
          cat > frontend/.build-info.json << EOF
          {
            "gitSha": "${{ steps.git_info.outputs.sha_short }}",
            "gitShaFull": "${{ steps.git_info.outputs.sha }}",
            "gitBranch": "${{ steps.git_info.outputs.branch }}",
            "buildTimestamp": "${{ steps.git_info.outputs.timestamp }}",
            "buildNumber": "${{ github.run_number }}",
            "buildId": "${{ github.run_id }}"
          }
          EOF

      - name: Build and Push API
        uses: docker/build-push-action@v5
        with:
          context: .
          file: backend/YendorCats.API/Dockerfile
          platforms: linux/amd64
          push: true
          tags: |
            ${{ env.ECR_REGISTRY }}/yendorcats-api:${{ steps.git_info.outputs.sha_short }}
            ${{ env.ECR_REGISTRY }}/yendorcats-api:latest
            ${{ env.ECR_REGISTRY }}/yendorcats-api:build-${{ github.run_number }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max
          build-args: |
            GIT_SHA=${{ steps.git_info.outputs.sha_short }}
            GIT_BRANCH=${{ steps.git_info.outputs.branch }}
            BUILD_TIMESTAMP=${{ steps.git_info.outputs.timestamp }}
          no-cache: ${{ github.event.inputs.force_rebuild == 'true' }}

      - name: Build and Push Frontend
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile.frontend.ci
          platforms: linux/amd64
          push: true
          tags: |
            ${{ env.ECR_REGISTRY }}/yendorcats-frontend:${{ steps.git_info.outputs.sha_short }}
            ${{ env.ECR_REGISTRY }}/yendorcats-frontend:latest
            ${{ env.ECR_REGISTRY }}/yendorcats-frontend:build-${{ github.run_number }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max
          build-args: |
            ASSET_VERSION=${{ steps.git_info.outputs.sha_short }}
            GIT_SHA=${{ steps.git_info.outputs.sha_short }}
            GIT_BRANCH=${{ steps.git_info.outputs.branch }}
          no-cache: ${{ github.event.inputs.force_rebuild == 'true' }}

      - name: Build and Push Uploader
        uses: docker/build-push-action@v5
        with:
          context: tools/file-uploader
          file: tools/file-uploader/Dockerfile
          platforms: linux/amd64
          push: true
          tags: |
            ${{ env.ECR_REGISTRY }}/yendorcats-uploader:${{ steps.git_info.outputs.sha_short }}
            ${{ env.ECR_REGISTRY }}/yendorcats-uploader:latest
            ${{ env.ECR_REGISTRY }}/yendorcats-uploader:build-${{ github.run_number }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max
          no-cache: ${{ github.event.inputs.force_rebuild == 'true' }}

      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      - name: Create Deployment Manifest
        run: |
          cat > deployment-manifest.json << EOF
          {
            "version": "1.0",
            "deployment": {
              "gitSha": "${{ steps.git_info.outputs.sha_short }}",
              "gitShaFull": "${{ steps.git_info.outputs.sha }}",
              "gitBranch": "${{ steps.git_info.outputs.branch }}",
              "buildNumber": "${{ github.run_number }}",
              "buildId": "${{ github.run_id }}",
              "buildTimestamp": "${{ steps.git_info.outputs.timestamp }}",
              "images": {
                "api": "${{ env.ECR_REGISTRY }}/yendorcats-api:${{ steps.git_info.outputs.sha_short }}",
                "frontend": "${{ env.ECR_REGISTRY }}/yendorcats-frontend:${{ steps.git_info.outputs.sha_short }}",
                "uploader": "${{ env.ECR_REGISTRY }}/yendorcats-uploader:${{ steps.git_info.outputs.sha_short }}"
              }
            }
          }
          EOF

      - name: Upload Deployment Manifest
        uses: actions/upload-artifact@v3
        with:
          name: deployment-manifest
          path: deployment-manifest.json

  deploy:
    name: Deploy to Production
    needs: build-and-push
    runs-on: ubuntu-latest
    if: |
      (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master') ||
      github.event.inputs.deploy_to_production == 'true'
    
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Deploy to EC2
        env:
          EC2_HOST: ${{ secrets.EC2_HOST }}
          EC2_USER: ${{ secrets.EC2_USER }}
          EC2_SSH_KEY: ${{ secrets.EC2_SSH_KEY }}
        run: |
          # Setup SSH
          mkdir -p ~/.ssh
          echo "$EC2_SSH_KEY" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key
          
          # Add host to known hosts
          ssh-keyscan -H $EC2_HOST >> ~/.ssh/known_hosts
          
          # Deploy using the ci-deploy.sh script on the server
          ssh -i ~/.ssh/deploy_key $EC2_USER@$EC2_HOST << 'ENDSSH'
            cd /home/<USER>/yendorcats.com
            if [ -f ci-deploy.sh ]; then
              ./ci-deploy.sh ${{ needs.build-and-push.outputs.git_sha_short }}
            else
              echo "ci-deploy.sh not found, using fallback deployment"
              # Fallback deployment commands
              docker-compose -f docker-compose.production.yml pull
              docker-compose -f docker-compose.production.yml down
              docker-compose -f docker-compose.production.yml up -d
            fi
          ENDSSH

      - name: Health Check
        run: |
          echo "Waiting for services to be healthy..."
          sleep 30
          
          # Check if the API is responding
          response=$(curl -s -o /dev/null -w "%{http_code}" http://${{ secrets.EC2_HOST }}/api/health)
          if [ "$response" = "200" ]; then
            echo "✅ API is healthy"
          else
            echo "❌ API health check failed with status: $response"
            exit 1
          fi

      - name: Send Deployment Notification
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          text: |
            Deployment ${{ job.status == 'success' && 'succeeded' || 'failed' }}
            Version: ${{ needs.build-and-push.outputs.git_sha_short }}
            Branch: ${{ github.ref_name }}
            Triggered by: ${{ github.actor }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        continue-on-error: true
