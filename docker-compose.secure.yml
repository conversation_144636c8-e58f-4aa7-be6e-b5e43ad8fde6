version: '3.8'

services:
  # Backend API service
  api:
    build:
      context: .
      dockerfile: backend/YendorCats.API/Dockerfile
    image: yendorcats/api:latest
    container_name: yendorcats-api
    restart: unless-stopped
    ports:
      - "5003:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Development}
      - ASPNETCORE_URLS=http://+:80
      - AWS__Region=us-west-004
      - AWS__UseCredentialsFromSecrets=true
      - AWS__S3__BucketName=${AWS_S3_BUCKET_NAME:-yendor}
      - AWS__S3__UseDirectS3Urls=true
      - AWS__S3__ServiceUrl=https://s3.us-west-004.backblazeb2.com
      - AWS__S3__PublicUrl=https://f004.backblazeb2.com/file/${AWS_S3_BUCKET_NAME:-yendor}/{key}
      - AWS__S3__UseCdn=false
      # Non-sensitive configuration
    volumes:
      - api-data:/app/data # SQLite database persistence
      - api-logs:/app/Logs
      # Mount secrets as files
      - ./secrets/aws_s3_access_key:/run/secrets/aws_s3_access_key:ro
      - ./secrets/aws_s3_secret_key:/run/secrets/aws_s3_secret_key:ro
      - ./secrets/b2_application_key_id:/run/secrets/b2_application_key_id:ro
      - ./secrets/b2_application_key:/run/secrets/b2_application_key:ro
      - ./secrets/b2_bucket_id:/run/secrets/b2_bucket_id:ro
      - ./secrets/mysql_user:/run/secrets/mysql_user:ro
      - ./secrets/mysql_password:/run/secrets/mysql_password:ro
      - ./secrets/jwt_secret:/run/secrets/jwt_secret:ro
    depends_on:
      - db
    networks:
      - yendorcats-network
    secrets:
      - aws_s3_access_key
      - aws_s3_secret_key
      - b2_application_key_id
      - b2_application_key
      - b2_bucket_id
      - mysql_user
      - mysql_password
      - jwt_secret

  # MariaDB Database
  db:
    image: mariadb:10.11
    container_name: yendorcats-db
    restart: unless-stopped
    environment:
      - MYSQL_DATABASE=YendorCats
      # Root password should be in secrets, but for simplicity we'll use environment
      # In production, use secrets for all credentials
    volumes:
      - mariadb-data:/var/lib/mysql
      # Mount secrets for database credentials
      - ./secrets/mysql_root_password:/run/secrets/mysql_root_password:ro
      - ./secrets/mysql_user:/run/secrets/mysql_user:ro
      - ./secrets/mysql_password:/run/secrets/mysql_password:ro
    networks:
      - yendorcats-network
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    secrets:
      - mysql_root_password
      - mysql_user
      - mysql_password

  # File Upload Service
  uploader:
    build:
      context: ./tools/file-uploader
      dockerfile: Dockerfile
    image: yendorcats/uploader:latest
    container_name: yendorcats-uploader
    restart: unless-stopped
    ports:
      - "5002:80"
    environment:
      - AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME:-yendor}
      - AWS_S3_REGION=us-west-004
      - AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com
      - API_BASE_URL=http://api
    volumes:
      # Mount secrets for AWS credentials
      - ./secrets/aws_s3_access_key:/run/secrets/aws_s3_access_key:ro
      - ./secrets/aws_s3_secret_key:/run/secrets/aws_s3_secret_key:ro
    depends_on:
      - api
    networks:
      - yendorcats-network
    secrets:
      - aws_s3_access_key
      - aws_s3_secret_key

networks:
  yendorcats-network:
    driver: bridge

volumes:
  api-data:
  api-logs:
  mariadb-data:

secrets:
  aws_s3_access_key:
    file: ./secrets/aws_s3_access_key
  aws_s3_secret_key:
    file: ./secrets/aws_s3_secret_key
  b2_application_key_id:
    file: ./secrets/b2_application_key_id
  b2_application_key:
    file: ./secrets/b2_application_key
  b2_bucket_id:
    file: ./secrets/b2_bucket_id
  mysql_root_password:
    file: ./secrets/mysql_root_password
  mysql_user:
    file: ./secrets/mysql_user
  mysql_password:
    file: ./secrets/mysql_password
  jwt_secret:
    file: ./secrets/jwt_secret