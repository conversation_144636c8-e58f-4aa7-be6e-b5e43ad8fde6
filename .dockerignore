# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
*.md

# IDE and editor files
.vscode/
.vs/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node_modules
node_modules/
npm-debug.log*

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
bin/
obj/
out/
build/
dist/

# Test results
TestResults/
*.trx
*.coverage
*.coveragexml

# Temporary files
tmp/
temp/

# Frontend build artifacts
frontend/node_modules/
frontend/dist/
frontend/build/

# Tools
tools/node_modules/
tools/dist/
tools/build/

# Large files that shouldn't be in Docker context
*.iso
*.dmg
*.zip
*.tar
*.tar.gz
*.rar

# Database files
*.db
*.sqlite
*.sqlite3

# Cache directories
.cache/
.parcel-cache/

# Python virtual environments
venv/
env/
.venv/
.env/
scripts/venv/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
pip-log.txt
pip-delete-this-directory.txt
