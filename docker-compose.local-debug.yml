version: '3.8'

services:
  # Backend API service - Built locally for debugging
  api:
    build:
      context: .
      dockerfile: backend/YendorCats.API/Dockerfile
    image: yendorcats-api:local-debug
    container_name: yendorcats-api-debug
    restart: unless-stopped
    ports:
      - "5003:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - CONTAINERIZED_BUILD=true
      - AWS__Region=us-west-004
      - AWS__UseCredentialsFromSecrets=false
      - AWS__S3__BucketName=${AWS_S3_BUCKET_NAME:-yendor}
      - AWS__S3__UseDirectS3Urls=true
      - AWS__S3__ServiceUrl=https://s3.us-west-004.backblazeb2.com
      - AWS__S3__PublicUrl=https://f004.backblazeb2.com/file/${AWS_S3_BUCKET_NAME:-yendor}/{key}
      - AWS__S3__UseCdn=true
      - AWS__S3__AccessKey=${AWS_S3_ACCESS_KEY}
      - AWS__S3__SecretKey=${AWS_S3_SECRET_KEY}
      - AWS__S3__KeyPrefix=YendorCats-General-SiteAccess/
      - B2_APPLICATION_KEY_ID=${B2_APPLICATION_KEY_ID}
      - B2_APPLICATION_KEY=${B2_APPLICATION_KEY}
      - B2_BUCKET_ID=${B2_BUCKET_ID}
      - ConnectionStrings__DefaultConnection=Server=db;Database=YendorCats;User=${MYSQL_USER:-yendorcats};Password=${MYSQL_PASSWORD:-password123};Port=3306;
      - JwtSettings__Secret=${YENDOR_JWT_SECRET:-your-super-secure-jwt-secret-32-characters-minimum-for-development}
      # CORS Configuration for local debugging
      - SERVER__ExternalIP=localhost
      - CORS__AdditionalOrigins=http://localhost,http://localhost:80,http://localhost:8080,http://127.0.0.1,http://127.0.0.1:80,http://127.0.0.1:8080
      # Force permissive CORS for debugging
      - FORCE_PERMISSIVE_CORS=true
    volumes:
      - api-data-debug:/app/data
      - api-logs-debug:/app/Logs
    depends_on:
      - db
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - yendorcats-debug

  # MariaDB Database
  db:
    image: mariadb:10.11
    container_name: yendorcats-db-debug
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-rootpassword123}
      - MYSQL_USER=${MYSQL_USER:-yendorcats}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-password123}
      - MYSQL_DATABASE=YendorCats
    volumes:
      - mariadb-data-debug:/var/lib/mysql
    ports:
      - "3306:3306"  # Expose for debugging
    networks:
      - yendorcats-debug
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-rootpassword123}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # File Upload Service
  uploader:
    build:
      context: tools/file-uploader
      dockerfile: Dockerfile
    image: yendorcats-uploader:local-debug
    container_name: yendorcats-uploader-debug
    restart: unless-stopped
    ports:
      - "5002:80"
    environment:
      - AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME:-yendor}
      - AWS_S3_REGION=us-west-004
      - AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com
      - AWS_S3_ACCESS_KEY=${AWS_S3_ACCESS_KEY}
      - AWS_S3_SECRET_KEY=${AWS_S3_SECRET_KEY}
      - API_BASE_URL=http://api
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - yendorcats-debug

  # Frontend with Nginx - Built locally for debugging
  frontend:
    build:
      context: frontend
      dockerfile: Dockerfile
      args:
        BUILD_VERSION: local-debug
        NGINX_CONFIG: development
    image: yendorcats-frontend:local-debug
    container_name: yendorcats-frontend-debug
    ports:
      - "8080:80"  # Different port to avoid conflicts
    depends_on:
      api:
        condition: service_healthy
      uploader:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - yendorcats-debug
    environment:
      - API_HOST=api
      - UPLOADER_HOST=uploader
      - NGINX_CONFIG=development

networks:
  yendorcats-debug:
    driver: bridge
    name: yendorcats-debug

volumes:
  api-data-debug:
    driver: local
  api-logs-debug:
    driver: local
  mariadb-data-debug:
    driver: local
