#!/bin/bash

# <PERSON>ript to map Ollama models to their blob files and show their sizes

echo "Ollama Model to Blob Mapping"
echo "============================"
echo ""

MANIFEST_DIR="$HOME/.ollama/models/manifests"
BLOB_DIR="$HOME/.ollama/models/blobs"

# Requires 'jq' for JSON parsing. If not installed, install it via Homebrew: brew install jq

# Find all manifest files from registry.ollama.ai only
find "$MANIFEST_DIR/registry.ollama.ai" -type f ! -name ".DS_Store" 2>/dev/null | sort | while read -r manifest; do
    # Extract model name from path
    model_path="${manifest#$MANIFEST_DIR/}"
    model_name=$(echo "$model_path" | sed 's|registry.ollama.ai/||' | sed 's|library/||')
    
    echo "Model: $model_name"
    echo "Path: $manifest"
    
    # Read the manifest and extract blob digests
    if [ -f "$manifest" ]; then
        # Extract SHA256 digests from JSON content using jq if available
        if command -v jq >/dev/null 2>&1; then
            blobs=$(jq -r '.config.digest, .layers[].digest' "$manifest" 2>/dev/null | grep '^sha256:' | sort -u)
        else
            # Fallback to grep if jq not available
            blobs=$(cat "$manifest" | grep -o '"sha256:[a-f0-9]\{64\}"' 2>/dev/null | tr -d '"' | sort -u)
        fi
        
        total_size=0
        echo "Blobs:"
        
        for blob in $blobs; do
            blob_file="$BLOB_DIR/$blob"
            if [ -f "$blob_file" ]; then
                # Get size in human-readable format
                size=$(ls -lh "$blob_file" | awk '{print $5}')
                # Get size in bytes for total calculation
                size_bytes=$(ls -l "$blob_file" | awk '{print $5}')
                total_size=$((total_size + size_bytes))
                
                echo "  - $blob (Size: $size)"
            fi
        done
        
        # Convert total size to human-readable format
        if [ $total_size -gt 1073741824 ]; then
            total_human=$(echo "scale=2; $total_size / 1073741824" | bc)
            echo "Total Size: ${total_human}G"
        elif [ $total_size -gt 1048576 ]; then
            total_human=$(echo "scale=2; $total_size / 1048576" | bc)
            echo "Total Size: ${total_human}M"
        else
            total_human=$(echo "scale=2; $total_size / 1024" | bc)
            echo "Total Size: ${total_human}K"
        fi
    fi
    
    echo "----------------------------------------"
    echo ""
done

# Show total disk usage
echo "Total Ollama models disk usage:"
du -sh "$BLOB_DIR"
