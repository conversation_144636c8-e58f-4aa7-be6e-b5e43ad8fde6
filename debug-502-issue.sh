#!/bin/bash

echo "=== YendorCats 502 Debug Script ==="
echo "Timestamp: $(date)"
echo ""

# Check if containers are running
echo "1. Container Status:"
echo "==================="
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep yendorcats
echo ""

# Check container logs for errors
echo "2. Recent Frontend Logs:"
echo "========================"
docker logs --tail 20 yendorcats-frontend-production 2>&1 | head -20
echo ""

echo "3. Recent API Logs:"
echo "=================="
docker logs --tail 20 yendorcats-api-production 2>&1 | head -20
echo ""

# Test network connectivity between containers
echo "4. Network Connectivity Tests:"
echo "=============================="

# Test if frontend can reach API
echo "Frontend -> API connectivity:"
docker exec yendorcats-frontend-production sh -c "wget -qO- --timeout=5 http://api/health 2>&1 || echo 'FAILED: Cannot reach api:80'"

# Test if frontend can reach uploader
echo "Frontend -> Uploader connectivity:"
docker exec yendorcats-frontend-production sh -c "wget -qO- --timeout=5 http://uploader/health 2>&1 || echo 'FAILED: Cannot reach uploader:80'"

# Test API health directly
echo "Direct API health check:"
curl -s --max-time 5 http://localhost:5003/health 2>&1 || echo "FAILED: Cannot reach API on localhost:5003"

# Test uploader health directly
echo "Direct Uploader health check:"
curl -s --max-time 5 http://localhost:5002/health 2>&1 || echo "FAILED: Cannot reach Uploader on localhost:5002"

echo ""

# Check nginx configuration in frontend container
echo "5. Frontend Nginx Configuration:"
echo "==============================="
docker exec yendorcats-frontend-production sh -c "nginx -t 2>&1"
echo ""

# Check which nginx config is being used
echo "6. Active Nginx Config Check:"
echo "============================"
docker exec yendorcats-frontend-production sh -c "head -10 /etc/nginx/nginx.conf"
echo ""

# Check Docker network
echo "7. Docker Network Information:"
echo "============================="
docker network ls | grep yendorcats
docker network inspect yendorcats-production --format '{{range .Containers}}{{.Name}}: {{.IPv4Address}}{{"\n"}}{{end}}' 2>/dev/null || echo "Network not found"
echo ""

# Test API endpoints that are failing
echo "8. API Endpoint Tests:"
echo "====================="
echo "Testing /api/PublicGallery/category/queens:"
curl -s --max-time 10 http://localhost/api/PublicGallery/category/queens 2>&1 | head -5 || echo "FAILED"

echo ""
echo "Testing /api/v2/gallery/queens:"
curl -s --max-time 10 http://localhost/api/v2/gallery/queens 2>&1 | head -5 || echo "FAILED"

echo ""
echo "=== Debug Complete ==="
echo ""
echo "Common fixes:"
echo "1. Restart all services: docker-compose -f docker-compose.production.yml restart"
echo "2. Check if API is healthy: curl http://localhost:5003/health"
echo "3. Rebuild frontend with new config: docker-compose -f docker-compose.production.yml build frontend"
echo "4. Check nginx logs: docker logs yendorcats-frontend-production"
