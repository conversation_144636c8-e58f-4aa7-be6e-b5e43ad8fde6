# Yendor Cats CI/CD System Documentation

## Overview

This repository now includes a complete, automated CI/CD system that handles building, tagging, and deploying Docker images with proper git SHA-based versioning for cache busting.

## Key Features

- **Automated Git SHA Tagging**: Every build is tagged with the git commit SHA
- **Frontend Cache Busting**: Frontend assets include the git SHA for automatic browser cache invalidation
- **Dynamic Deployment**: Server can fetch and deploy the latest images automatically
- **Multiple Tag Strategies**: Images are tagged with SHA, branch name, and latest
- **GitHub Actions Integration**: Full CI/CD pipeline for automated deployments
- **Rollback Capability**: Easy rollback to previous versions using git SHAs

## Scripts

### 1. `ci-build-push.sh` (Local Development Machine)

**Purpose**: Build and push Docker images to AWS ECR with proper tagging

**Usage**:
```bash
# Standard build
./ci-build-push.sh

# Force rebuild without cache
./ci-build-push.sh --force-rebuild
```

**What it does**:
1. Checks for uncommitted changes and warns if present
2. Gets current git SHA and branch information
3. Logs into AWS ECR
4. Builds all three services (api, frontend, uploader)
5. Tags images with: git SHA (short), branch name, and "latest"
6. Pushes all images to ECR
7. Creates a deployment manifest

### 2. `ci-deploy.sh` (EC2 Server)

**Purpose**: Deploy images from ECR to production

**Usage**:
```bash
# Deploy specific tag
./ci-deploy.sh 74cde28

# Deploy latest pushed image
./ci-deploy.sh --latest

# Deploy latest from specific branch
./ci-deploy.sh --branch main
```

**What it does**:
1. Fetches the requested tag (or finds latest)
2. Verifies tag exists for all services
3. Updates docker-compose.yml with new tags
4. Pulls new images
5. Performs zero-downtime deployment
6. Runs health checks
7. Cleans up old images

### 3. GitHub Actions Workflow (`.github/workflows/ci-cd.yml`)

**Triggers**:
- Push to main/master/develop branches
- Pull requests
- Manual trigger with options

**Features**:
- Automated builds on every push
- Deployment to production on main/master branch
- Manual deployment option for other branches
- Build caching for faster builds
- Health checks after deployment
- Slack notifications (optional)

## Setup Instructions

### Local Development Setup

1. **Install Prerequisites**:
   ```bash
   # macOS
   brew install awscli jq docker
   
   # Ubuntu/Debian
   sudo apt-get update
   sudo apt-get install awscli jq docker.io docker-compose
   ```

2. **Configure AWS CLI**:
   ```bash
   aws configure
   # Enter your AWS Access Key ID
   # Enter your AWS Secret Access Key
   # Enter region: ap-southeast-2
   ```

3. **Make scripts executable**:
   ```bash
   chmod +x ci-build-push.sh ci-deploy.sh
   ```

### EC2 Server Setup

1. **Copy deployment script to server**:
   ```bash
   scp ci-deploy.sh ubuntu@your-server:/home/<USER>/yendorcats.com/
   ```

2. **On the server, make it executable**:
   ```bash
   ssh ubuntu@your-server
   cd /home/<USER>/yendorcats.com
   chmod +x ci-deploy.sh
   ```

3. **Ensure AWS CLI is configured on server**:
   ```bash
   aws configure
   ```

### GitHub Actions Setup

1. **Add secrets to GitHub repository**:
   
   Go to Settings → Secrets and variables → Actions, then add:
   
   - `AWS_ACCESS_KEY_ID`: Your AWS access key
   - `AWS_SECRET_ACCESS_KEY`: Your AWS secret key
   - `EC2_HOST`: Your EC2 server IP or domain
   - `EC2_USER`: SSH username (usually `ubuntu`)
   - `EC2_SSH_KEY`: Private SSH key for EC2 access
   - `SLACK_WEBHOOK`: (Optional) Slack webhook for notifications

2. **Commit and push the workflow file**:
   ```bash
   git add .github/workflows/ci-cd.yml
   git commit -m "Add CI/CD workflow"
   git push
   ```

## Deployment Workflow

### Manual Deployment (Recommended for now)

1. **On your local machine**:
   ```bash
   # Commit your changes
   git add .
   git commit -m "Your commit message"
   git push
   
   # Build and push images
   ./ci-build-push.sh
   ```

2. **On the EC2 server**:
   ```bash
   # Deploy the latest images
   cd /home/<USER>/yendorcats.com
   ./ci-deploy.sh --latest
   ```

### Automated Deployment (via GitHub Actions)

1. **Push to main branch**:
   ```bash
   git checkout main
   git merge develop
   git push
   ```
   
   The GitHub Action will automatically:
   - Build all images
   - Push to ECR
   - Deploy to production
   - Run health checks

### Rollback Procedure

If you need to rollback to a previous version:

1. **Find the previous git SHA**:
   ```bash
   git log --oneline -10
   ```

2. **Deploy that specific version**:
   ```bash
   # On the EC2 server
   ./ci-deploy.sh abc123f  # Replace with actual SHA
   ```

## Frontend Cache Busting

The frontend automatically includes the git SHA in all asset URLs:

- JavaScript files: `main.js?v=74cde28`
- CSS files: `styles.css?v=74cde28`
- The version is embedded during the Docker build process
- Browsers automatically fetch new versions when the SHA changes

## Troubleshooting

### Build Issues

1. **"Docker daemon not running"**:
   ```bash
   # macOS
   open -a Docker
   
   # Linux
   sudo systemctl start docker
   ```

2. **"AWS credentials not found"**:
   ```bash
   aws configure
   ```

3. **"ECR repository not found"**:
   The script will automatically create repositories if they don't exist.

### Deployment Issues

1. **"Tag not found in ECR"**:
   - Ensure the build completed successfully
   - Check ECR for available tags:
     ```bash
     aws ecr describe-images --repository-name yendorcats-api --region ap-southeast-2
     ```

2. **"Health check failed"**:
   - Check container logs:
     ```bash
     docker-compose -f docker-compose.production.yml logs api
     ```

3. **"Permission denied"**:
   - Ensure scripts are executable: `chmod +x *.sh`

## Best Practices

1. **Always test locally first**:
   ```bash
   docker-compose -f docker-compose.yml up --build
   ```

2. **Use meaningful commit messages**: They help identify deployments

3. **Monitor after deployment**:
   ```bash
   # Watch logs
   docker-compose -f docker-compose.production.yml logs -f
   
   # Check health
   curl http://your-server/api/health
   ```

4. **Keep deployment manifests**: They provide deployment history

## Architecture

```
Local Development          GitHub              AWS ECR              EC2 Production
      |                      |                    |                      |
   [Code] ----push----→ [Actions] ---build--→ [Images] ←--pull--- [Containers]
      |                      |                    |                      |
   [ci-build-push.sh]    [ci-cd.yml]         [Tagged Images]      [ci-deploy.sh]
```

## Security Notes

- Never commit AWS credentials to the repository
- Use GitHub Secrets for sensitive information
- Regularly rotate AWS access keys
- Use IAM roles with minimal required permissions
- Keep the EC2 SSH key secure and never share it

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review container logs for error messages
3. Ensure all prerequisites are installed
4. Verify AWS credentials and permissions

---

*Last updated: August 2025*
