# Traefik Configuration for YendorCats Production
# Static configuration file

# Global configuration
global:
  checkNewVersion: false
  sendAnonymousUsage: false

# API and Dashboard configuration
api:
  dashboard: true
  debug: false
  insecure: false

# Entry points configuration
entryPoints:
  web:
    address: ":80"
    http:
      redirections:
        entrypoint:
          to: websecure
          scheme: https
          permanent: true

  websecure:
    address: ":443"
    http:
      tls:
        options: default

# Certificate resolvers
certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>  # Replace with your email
      storage: /data/acme.json
      httpChallenge:
        entryPoint: web
      # Use DNS challenge for wildcard certificates (optional)
      # dnsChallenge:
      #   provider: cloudflare
      #   resolvers:
      #     - "*******:53"
      #     - "*******:53"

# Providers configuration
providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: yendorcats-production
    watch: true

  file:
    filename: /etc/traefik/dynamic.yml
    watch: true

# Logging configuration
log:
  level: INFO
  filePath: "/data/traefik.log"
  format: json

# Access logs
accessLog:
  filePath: "/data/access.log"
  format: json
  bufferingSize: 100
  filters:
    statusCodes:
      - "400-499"
      - "500-599"

# Metrics (optional - for monitoring)
metrics:
  prometheus:
    addEntryPointsLabels: true
    addServicesLabels: true
    addRoutersLabels: true

# Pilot (optional - Traefik Cloud integration)
# pilot:
#   token: "your-pilot-token"

# Tracing (optional - for debugging)
# tracing:
#   jaeger:
#     samplingServerURL: http://jaeger:14268/api/sampling
#     localAgentHostPort: jaeger:6831
