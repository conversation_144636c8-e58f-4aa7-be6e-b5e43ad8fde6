# Traefik Dynamic Configuration
# Runtime configuration for middlewares, TLS, etc.

# TLS Configuration
tls:
  options:
    default:
      sslProtocols:
        - "TLSv1.2"
        - "TLSv1.3"
      cipherSuites:
        - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
        - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
        - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
        - "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256"
      minVersion: "VersionTLS12"
      maxVersion: "VersionTLS13"

# HTTP Middlewares
http:
  middlewares:
    # Security headers
    security-headers:
      headers:
        accessControlAllowMethods:
          - GET
          - OPTIONS
          - PUT
          - POST
          - DELETE
        accessControlMaxAge: 100
        hostsProxyHeaders:
          - "X-Forwarded-Host"
        referrerPolicy: "same-origin"
        customRequestHeaders:
          X-Forwarded-Proto: "https"
        customResponseHeaders:
          X-Robots-Tag: "none,noarchive,nosnippet,notranslate,noimageindex"
          X-Frame-Options: "DENY"
          X-Content-Type-Options: "nosniff"
          Referrer-Policy: "same-origin"
          Strict-Transport-Security: "max-age=63072000; includeSubDomains; preload"
          Content-Security-Policy: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; media-src 'self'; object-src 'none'; child-src 'none'; worker-src 'none'; frame-ancestors 'none'; form-action 'self'; base-uri 'self'; manifest-src 'self';"

    # Rate limiting
    rate-limit:
      rateLimit:
        average: 100
        period: 1m
        burst: 50

    # API rate limiting (more restrictive)
    api-rate-limit:
      rateLimit:
        average: 50
        period: 1m
        burst: 20

    # Basic authentication for admin interfaces
    admin-auth:
      basicAuth:
        users:
          - "admin:$2y$10$..." # Generate with: htpasswd -nb admin password

    # Compression
    compression:
      compress: {}

    # CORS for API
    cors:
      headers:
        accessControlAllowOriginList:
          - "https://yourdomain.com"
          - "https://www.yourdomain.com"
        accessControlAllowMethods:
          - "GET"
          - "POST"
          - "PUT"
          - "DELETE"
          - "OPTIONS"
        accessControlAllowHeaders:
          - "Content-Type"
          - "Authorization"
          - "X-Requested-With"
        accessControlExposeHeaders:
          - "Content-Length"
          - "Content-Range"
        accessControlAllowCredentials: true
        accessControlMaxAge: 100

    # Redirect www to non-www
    redirect-www:
      redirectRegex:
        regex: "^https://www\\.(.+)"
        replacement: "https://${1}"
        permanent: true

    # IP whitelist for admin (optional)
    # admin-whitelist:
    #   ipWhiteList:
    #     sourceRange:
    #       - "127.0.0.1/32"
    #       - "***********/24"
    #       - "your.ip.address/32"

  # Services (if needed for manual configuration)
  services:
    # Example service configuration
    # api-service:
    #   loadBalancer:
    #     servers:
    #       - url: "http://yendorcats-api:80"
    #     healthCheck:
    #       path: "/health"
    #       interval: "30s"
    #       timeout: "10s"

  # Routers (if needed for manual configuration)
  routers:
    # Dashboard router with authentication
    dashboard-secure:
      rule: "Host(`traefik.yourdomain.com`)"
      service: "api@internal"
      middlewares:
        - "admin-auth"
        - "security-headers"
        - "rate-limit"
      tls:
        certResolver: "letsencrypt"

    # Redirect www to non-www
    www-redirect:
      rule: "Host(`www.yourdomain.com`)"
      middlewares:
        - "redirect-www"
      service: "noop@internal"
      tls:
        certResolver: "letsencrypt"
