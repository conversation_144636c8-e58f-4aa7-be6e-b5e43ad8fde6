#!/bin/bash

# Check if dry-run mode
DRY_RUN=false
if [ "$1" = "--dry-run" ]; then
  DRY_RUN=true
  echo "DRY RUN MODE - No files will be moved"
  echo "========================================"
fi

# Define models and their manifest paths
MODEL_NAMES=(
  "qwen2.5vl/32b"
  "deepseek-r1/14b"
  "llava/13b"
  "devstral/24b"
  "saki007ster/CybersecurityRiskAnalyst/latest"
)

MODEL_PATHS=(
  "$HOME/.ollama/models/manifests/registry.ollama.ai/library/qwen2.5vl/32b"
  "$HOME/.ollama/models/manifests/registry.ollama.ai/library/deepseek-r1/14b"
  "$HOME/.ollama/models/manifests/registry.ollama.ai/library/llava/13b"
  "$HOME/.ollama/models/manifests/registry.ollama.ai/library/devstral/24b"
  "$HOME/.ollama/models/manifests/registry.ollama.ai/saki007ster/CybersecurityRiskAnalyst/latest"
)

# Directories
BLOB_DIR="$HOME/.ollama/models/blobs"
TARGET_DIR="/Volumes/virtualpocket"

# Check if target volume is writable
if [ ! -w "$TARGET_DIR" ]; then
  echo "ERROR: Target volume $TARGET_DIR is not writable!"
  echo "Please remount the volume with write permissions or choose a different target."
  exit 1
fi

# Create target folder on SD card
if [ "$DRY_RUN" = false ]; then
  mkdir -p "$TARGET_DIR/ollama_blobs" || {
    echo "ERROR: Cannot create directory on $TARGET_DIR"
    exit 1
  }
else
  echo "Would create: $TARGET_DIR/ollama_blobs"
fi

# Track total size
TOTAL_SIZE=0

# Move and symlink
for i in "${!MODEL_NAMES[@]}"; do
  MODEL="${MODEL_NAMES[$i]}"
  MANIFEST_PATH="${MODEL_PATHS[$i]}"
  
  echo ""
  echo "Processing model: $MODEL"
  
  if [ ! -f "$MANIFEST_PATH" ]; then
    echo "Manifest not found: $MANIFEST_PATH"
    continue
  fi
  
  BLOBS=$(python3 -c "import json
with open('$MANIFEST_PATH', 'r') as f:
    data = json.load(f)
    blobs = [layer['digest'] for layer in data.get('layers', [])] + [data['config']['digest']]
    print(' '.join(blobs))")
  
  MODEL_SIZE=0
  for BLOB in $BLOBS; do
    BLOB_NAME=${BLOB//:/-}  # Replace ':' with '-' for filesystem
    SOURCE_PATH="$BLOB_DIR/$BLOB_NAME"
    TARGET_PATH="$TARGET_DIR/ollama_blobs/$BLOB_NAME"
    
    if [ -f "$SOURCE_PATH" ]; then
      FILE_SIZE=$(stat -f%z "$SOURCE_PATH" 2>/dev/null || stat -c%s "$SOURCE_PATH" 2>/dev/null || echo 0)
      FILE_SIZE_MB=$((FILE_SIZE / 1024 / 1024))
      MODEL_SIZE=$((MODEL_SIZE + FILE_SIZE))
      
      if [ "$DRY_RUN" = true ]; then
        echo "  Would move: $BLOB_NAME (${FILE_SIZE_MB}MB)"
        echo "    From: $SOURCE_PATH"
        echo "    To:   $TARGET_PATH"
      else
        echo "  Moving $BLOB_NAME (${FILE_SIZE_MB}MB)..."
        # Move to SD card
        mv "$SOURCE_PATH" "$TARGET_PATH"
        # Create symlink
        ln -s "$TARGET_PATH" "$SOURCE_PATH"
        echo "  Created symlink: $SOURCE_PATH -> $TARGET_PATH"
      fi
    else
      echo "  Blob not found: $BLOB_NAME"
    fi
  done
  
  MODEL_SIZE_GB=$((MODEL_SIZE / 1024 / 1024 / 1024))
  TOTAL_SIZE=$((TOTAL_SIZE + MODEL_SIZE))
  echo "  Model size: ${MODEL_SIZE_GB}GB"
  echo "---"
done

TOTAL_SIZE_GB=$((TOTAL_SIZE / 1024 / 1024 / 1024))
echo ""
echo "Total size to be moved: ${TOTAL_SIZE_GB}GB"

echo "Models moved and symlinked successfully."
