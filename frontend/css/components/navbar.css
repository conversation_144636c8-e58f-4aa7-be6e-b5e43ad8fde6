/*
 * Yendor Cats - Navbar Styles
 * Includes expanding landing page functionality
 */

.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: var(--bg-primary);
    background-image: url('../../images/logo_shield.webp');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transition: all var(--transition-medium);
    z-index: var(--z-navbar);
    
    /* Default state - expanded full height */
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Collapsed state for when user scrolls down */
.header.scrolled {
    height: 90px;
    box-shadow: var(--shadow-md);
    justify-content: flex-start;
    background-color: var(--bg-navbar);
    background-image: url('../../images/logo_shield.webp');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
}

.header.scrolled::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--bg-navbar);
    opacity: 0.85;
    z-index: -1;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Container adjustments */
.header .container {
    padding: 0;
    max-width: 100%;
}

/* Logo styling (yendor cats logo in navbar) - Left aligned */
.logo {
    z-index: calc(var(--z-navbar) + 10);
    flex: 0 0 auto;
    margin-left: 0;
    padding-left: 20px;
    margin-top: 5px;
    order: 1; /* Ensure logo stays on the left */
}

.logo a {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo h1 {
    font-size: 2.2rem;
    color: var(--text-primary);
    margin-bottom: 0;
    font-weight: 700;
}

.logo h1 span {
    color: var(--accent-primary);
    font-weight: 800;
}

.header.scrolled .logo h1 {
    color: var(--text-navbar);
    font-size: 2.2rem;
    font-weight: 700;
}

.header.scrolled .logo h1 span {
    color: #ff0000; /* Darker red for better visibility */
    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.5);
    font-weight: 800;
}

/* Navigation styling */
.main-nav {
    display: flex;
    align-items: center;
    flex: 1 1 auto;
    justify-content: flex-end;
    margin-left: auto;
    padding-right: 2rem;
    margin-top: 5px;
    order: 2; /* Ensure nav stays on the right */
}

.nav-list {
    display: none;
    list-style: none;
    margin: 0;
    padding: 0;
    
    /* Center in fullscreen mode */
    text-align: center;
}

.nav-list.active {
    display: flex !important;
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--bg-primary);
    z-index: calc(var(--z-navbar) + 20); /* Higher than logo z-index to appear on top */
    justify-content: center;
    align-items: center;
}

.header.scrolled .nav-list.active {
    background-color: var(--bg-navbar);
}

.nav-list li {
    margin: 0.7rem 0;
}

.nav-list li a {
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.2rem;
    text-decoration: none;
    padding: 0.5rem 1rem;
    transition: color var(--transition-fast);
    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.5px;
}

.header.scrolled .nav-list li a {
    color: var(--text-navbar);
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
    font-weight: 700;
}

.nav-list li a:hover,
.nav-list li a.active {
    color: var(--accent-primary);
}

.header.scrolled .nav-list li a:hover,
.header.scrolled .nav-list li a.active {
    color: #ff0000; /* Darker red for better visibility */
}

.nav-list li a.btn-primary {
    background-color: var(--accent-primary);
    color: var(--text-navbar);
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-md);
}

.header.scrolled .nav-list li a.btn-primary {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.nav-list li a.btn-primary:hover {
    background-color: var(--accent-secondary);
}

.header.scrolled .nav-list li a.btn-primary:hover {
    background-color: var(--bg-secondary);
}

/* Login link styling - less prominent */
.nav-list li a.login-link {
    font-size: 0.9rem !important;
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 400 !important;
    text-decoration: underline;
    padding: 0.3rem 0.5rem !important;
}

.header.scrolled .nav-list li a.login-link {
    color: rgba(0, 0, 0, 0.6) !important;
}

.nav-list li a.login-link:hover {
    color: var(--accent-primary) !important;
    text-decoration: none;
}

.header.scrolled .nav-list li a.login-link:hover {
    color: #ff0000 !important;
}

/* Mobile menu toggle */
.mobile-menu-toggle {
    display: block;
    z-index: calc(var(--z-navbar) + 25); /* Highest z-index to stay on top of everything */
    width: 30px;
    height: 25px;
    position: relative;
    background: none;
    border: none;
    cursor: pointer;
    padding: 10px;
    margin-right: 10px;
}

.mobile-menu-toggle span {
    display: block;
    position: absolute;
    height: 3px;
    width: 100%;
    background: var(--accent-light);
    border-radius: 3px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: all var(--transition-fast);
}

.header.scrolled .mobile-menu-toggle span {
    background: #000000; /* Make the hamburger icon black for better visibility */
    height: 4px; /* Thicker lines */
}

.mobile-menu-toggle span:nth-child(1) {
    top: 0px;
}

.mobile-menu-toggle span:nth-child(2) {
    top: 10px;
}

.mobile-menu-toggle span:nth-child(3) {
    top: 20px;
}

.mobile-menu-toggle.active span:nth-child(1) {
    top: 10px;
    transform: rotate(135deg);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
    left: -30px;
}

.mobile-menu-toggle.active span:nth-child(3) {
    top: 10px;
    transform: rotate(-135deg);
}

/* Scroll indicator for landing page */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--text-primary);
    opacity: 1;
    transition: opacity var(--transition-medium);
}

.header.scrolled .scroll-indicator {
    opacity: 0;
    pointer-events: none;
}

.scroll-indicator p {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.scroll-indicator-arrow {
    width: 40px;
    height: 40px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Media queries */
@media (max-width: 767px) {
    .header-content {
        padding: 1rem var(--spacing-md);
    }
    
    .nav-list {
        display: none;
    }
    
    .nav-list.active {
        display: flex !important;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background-color: rgba(139, 0, 0, 0.98); /* Darker semi-transparent background for better readability */
        z-index: calc(var(--z-navbar) + 20); /* Higher than logo z-index to appear on top */
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 2rem;
        overflow-y: auto;
        backdrop-filter: blur(2px); /* Add blur effect for better focus on menu items */
    }
    
    .header.scrolled .nav-list.active {
        background-color: rgba(255, 215, 0, 0.98); /* Darker semi-transparent yellow for better readability */
        backdrop-filter: blur(2px); /* Add blur effect for better focus on menu items */
    }
    
    .nav-list.active li {
        margin: 1rem 0;
        opacity: 0;
        animation: fadeInItem 0.3s forwards;
        animation-delay: calc(0.1s * var(--item-index, 0));
    }
    
    .nav-list.active li:nth-child(1) { --item-index: 1; }
    .nav-list.active li:nth-child(2) { --item-index: 2; }
    .nav-list.active li:nth-child(3) { --item-index: 3; }
    .nav-list.active li:nth-child(4) { --item-index: 4; }
    .nav-list.active li:nth-child(5) { --item-index: 5; }
    .nav-list.active li:nth-child(6) { --item-index: 6; }
    
    @keyframes fadeInItem {
        0% {
            opacity: 0;
            transform: translateY(20px);
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .header.scrolled .nav-list.active li a {
        color: #000000; /* Black text on yellow background */
        font-size: 1.5rem;
        padding: 0.75rem 1.5rem;
        display: block;
        text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8); /* Light text shadow for better readability over logo */
        font-weight: 600; /* Slightly bolder text */
        border-radius: 8px; /* Rounded corners for better visual separation */
        background-color: rgba(255, 255, 255, 0.3); /* Subtle light background behind each link */
        margin: 0.5rem 0; /* Add some spacing between links */
        transition: all 0.3s ease; /* Smooth transitions */
    }
    
    .nav-list.active li a {
        color: #ffffff; /* White text on red background */
        font-size: 1.5rem;
        padding: 0.75rem 1.5rem;
        display: block;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8); /* Strong text shadow for better readability over logo */
        font-weight: 600; /* Slightly bolder text */
        border-radius: 8px; /* Rounded corners for better visual separation */
        background-color: rgba(0, 0, 0, 0.2); /* Subtle dark background behind each link */
        margin: 0.5rem 0; /* Add some spacing between links */
        transition: all 0.3s ease; /* Smooth transitions */
    }

    /* Hover effects for mobile navigation links */
    .nav-list.active li a:hover {
        background-color: rgba(0, 0, 0, 0.4); /* Darker background on hover */
        transform: scale(1.05); /* Slight scale effect */
    }

    .header.scrolled .nav-list.active li a:hover {
        background-color: rgba(255, 255, 255, 0.5); /* Lighter background on hover */
        transform: scale(1.05); /* Slight scale effect */
    }

    .mobile-menu-toggle {
        display: block;
        z-index: calc(var(--z-navbar) + 25); /* Highest z-index to stay on top of everything */
    }
}

@media (min-width: 768px) {
    .header.scrolled .container {
        max-width: 100%;
        padding: 0;
    }
    
    .header.scrolled .header-content {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        width: 100%;
        padding: 0.75rem 0;
    }
    
    .logo {
        margin-left: 0;
        padding-left: 20px;
        margin-top: 5px;
    }
    
    .main-nav {
        margin-left: auto;
        padding-right: 1.5rem;
        margin-top: 5px;
    }
    
    .mobile-menu-toggle {
        display: none;
    }
    
    .nav-list {
        display: flex;
        flex-direction: row;
        width: auto;
        margin-left: auto;
    }
    
    .nav-list li {
        margin: 0 1rem;
    }
    
    .nav-list li:last-child {
        margin-left: 2rem;
    }
    
    /* When scrolled, adjust the nav display */
    .header.scrolled .nav-list {
        flex-direction: row;
    }
    
    .header.scrolled {
        height: 80px;
    }
    
    /* Full height header adjustments */
    .header:not(.scrolled) .nav-list {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        position: absolute;
        right: 3rem;
        top: 50%;
        transform: translateY(-50%);
    }
    
    .header:not(.scrolled) .nav-list li {
        margin: 0.6rem 0;
    }
    
    .header:not(.scrolled) .nav-list li a {
        font-size: 1.1rem;
        padding: 0.4rem 0.8rem;
        color: var(--text-navbar);
    }
    
    .header:not(.scrolled) .logo {
        position: absolute;
        top: 2rem;
        left: 2rem;
        transform: none;
    }
    
    .header:not(.scrolled) .logo h1 {
        font-size: 3rem;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .nav-list li a {
        font-size: 1rem;
        padding: 0.5rem 0.5rem;
    }
    
    .header:not(.scrolled) .nav-list {
        right: 3%;
    }
    
    .header:not(.scrolled) .nav-list li a {
        font-size: 1.2rem;
        padding: 0.3rem 0.8rem;
    }
}

@media (min-width: 992px) {
    .header.scrolled .container {
        padding: 0;
    }
    
    .header.scrolled .header-content {
        max-width: 100%;
        margin: 0 auto;
        justify-content: flex-start;
        padding: 1rem 0;
    }
    
    .logo {
        margin-left: 0;
        padding-left: 30px;
        margin-top: 0;
    }
    
    .main-nav {
        margin-left: auto;
        padding-right: 2rem;
        margin-top: 0;
    }
    
    .nav-list {
        gap: 2rem;
    }
    
    .nav-list li {
        margin: 0;
    }
    
    .nav-list li a {
        font-size: 1.3rem;
        white-space: nowrap;
    }
    
    .nav-list li:last-child {
        margin-left: 2rem;
    }
    
    .header:not(.scrolled) .nav-list {
        right: 8%;
    }
    
    .header:not(.scrolled) .nav-list li a {
        font-size: 1.5rem;
        padding: 0.4rem 1.2rem;
    }
}

@media (min-width: 1200px) {
    .logo {
        margin-left: 0;
        padding-left: 40px;
    }
    
    .nav-list {
        gap: 3rem;
    }
    
    .main-nav {
        margin-left: auto;
        padding-right: 3rem;
    }
} 