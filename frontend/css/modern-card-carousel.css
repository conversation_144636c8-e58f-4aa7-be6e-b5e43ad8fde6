/**
 * Modern Card Carousel Styles for Yendor Cats
 * Responsive card-based carousel with metadata display and lightbox
 */

/* --------------------------------------------------
 * Modern Carousel Container
 * -------------------------------------------------- */
.modern-carousel {
    position: relative;
    width: 100%;
    margin: 2rem 0;
    overflow: hidden;
}

.carousel-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    border-radius: 12px;
}

.carousel-track {
    display: flex;
    transition: transform 0.3s ease;
    align-items: stretch;
}

/* --------------------------------------------------
 * Cat Cards
 * -------------------------------------------------- */
.cat-card {
    flex-shrink: 0;
    position: relative;
    background: #2d2d2d;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    margin-right: 20px;
}

.cat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.cat-card:last-child {
    margin-right: 0;
}

/* Card Image Container */
.card-image {
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.cat-card:hover .card-image img {
    transform: scale(1.05);
}

/* Card Metadata Overlay */
.card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 1.5rem 1rem 1rem;
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.card-overlay .cat-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.card-overlay .cat-age {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 400;
}

/* Hover Overlay with Actions */
.card-hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.cat-card:hover .card-hover-overlay {
    opacity: 1;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-expand-btn {
    background: rgba(111, 17, 0, 0.9);
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-primary, #333);
}

.card-expand-btn:hover {
    background: #404040;
    transform: scale(1.1);
}

/* Card Footer */
.card-footer {
    padding: 1rem;
    background: #2d2d2d;
    border-top: 1px solid #404040;
}

.photo-date {
    font-size: 0.85rem;
    color: var(--text-secondary, #666);
    font-weight: 500;
}

/* --------------------------------------------------
 * Navigation Controls
 * -------------------------------------------------- */
.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 48px;
    height: 48px;
    background: var(--accent-primary, #d4af37);
    border: none;
    border-radius: 50%;
    font-size: 1.5rem;
    color: var(--text-primary, #333);
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-nav:hover:not(:disabled) {
    background: #404040;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.carousel-nav:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.carousel-nav.prev {
    left: -24px;
}

.carousel-nav.next {
    right: -24px;
}

/* --------------------------------------------------
 * Indicators
 * -------------------------------------------------- */
.carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

.carousel-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: var(--accent-primary, #d4af37) 2px solid;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
}

.carousel-indicator.active,
.carousel-indicator:hover {
    background: var(--accent-primary, #d4af37);
    border-color: var(--accent-primary, #d4af37);
}

/* --------------------------------------------------
 * Lightbox Modal
 * -------------------------------------------------- */
.cat-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.cat-lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    cursor: pointer;
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    background: #2d2d2d;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
}

.lightbox-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    background: #404040;
}

.lightbox-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary, #333);
}

.lightbox-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background-color 0.3s ease;
    color: var(--text-secondary, #666);
}

.lightbox-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

.lightbox-body {
    display: flex;
    flex: 1;
    min-height: 0;
}

.lightbox-image-container {
    position: relative;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #404040;
}

.lightbox-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.lightbox-nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 48px;
    height: 48px;
    background: var(--accent-primary, #d4af37);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: var(--text-primary, #333);
}

.lightbox-nav-btn:hover {
    background: #404040;
    transform: translateY(-50%) scale(1.1);
}

.lightbox-nav-btn.prev {
    left: 1rem;
}

.lightbox-nav-btn.next {
    right: 1rem;
}

.lightbox-metadata {
    width: 300px;
    padding: 2rem;
    background: #2d2d2d;
    border-left: 1px solid #404040;
    overflow-y: auto;
}

.metadata-item {
    margin-bottom: 1.5rem;
}

.metadata-item label {
    display: block;
    font-weight: 600;
    color: var(--text-secondary, #666);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metadata-item span {
    font-size: 1.1rem;
    color: var(--text-primary, #333);
    font-weight: 500;
}

/* --------------------------------------------------
 * Loading and Error States
 * -------------------------------------------------- */
.loading-placeholder,
.error-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
    color: var(--text-secondary, #666);
}

.loading-spinner {
    margin-bottom: 1rem;
}

.spinner {
    width: 48px;
    height: 48px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--accent-primary, #d4af37);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-placeholder p {
    font-size: 1.1rem;
    margin: 0.5rem 0;
    font-weight: 500;
}

.loading-placeholder small {
    font-size: 0.9rem;
    opacity: 0.7;
}

.error-placeholder .error-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.retry-button {
    background: var(--accent-primary, #d4af37);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 1rem;
    transition: background-color 0.3s ease;
}

.retry-button:hover {
    background: var(--accent-primary-dark, #b8941f);
}

/* --------------------------------------------------
 * Responsive Design
 * -------------------------------------------------- */

/* Desktop Large (1200px+) */
@media (min-width: 1200px) {
    .cat-card {
        max-width: calc(25% - 15px);
    }
    
    .card-image {
        height: 280px;
    }
}

/* Desktop (992px - 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {
    .cat-card {
        max-width: calc(33.333% - 13px);
    }
    
    .card-image {
        height: 260px;
    }
}

/* Tablet (768px - 991px) */
@media (max-width: 991px) and (min-width: 768px) {
    .cat-card {
        max-width: calc(50% - 10px);
    }
    
    .card-image {
        height: 240px;
    }
    
    .carousel-nav {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .carousel-nav.prev {
        left: -20px;
    }
    
    .carousel-nav.next {
        right: -20px;
    }
    
    .lightbox-metadata {
        width: 250px;
        padding: 1.5rem;
    }
}

/* Mobile (767px and below) */
@media (max-width: 767px) {
    .modern-carousel {
        margin: 1rem 0;
    }
    
    .cat-card {
        max-width: 100%;
        margin-right: 15px;
    }
    
    .card-image {
        height: 200px;
    }
    
    .card-overlay {
        padding: 1rem 0.75rem 0.75rem;
    }
    
    .card-overlay .cat-name {
        font-size: 1rem;
    }
    
    .card-overlay .cat-age {
        font-size: 0.85rem;
    }
    
    .card-footer {
        padding: 0.75rem;
    }
    
    .carousel-nav {
        width: 36px;
        height: 36px;
        font-size: 1rem;
    }
    
    .carousel-nav.prev {
        left: -18px;
    }
    
    .carousel-nav.next {
        right: -18px;
    }
    
    .carousel-indicators {
        margin-top: 1rem;
    }
    
    .carousel-indicator {
        width: 10px;
        height: 10px;
    }
    
    /* Lightbox adjustments for mobile */
    .lightbox-content {
        max-width: 95vw;
        max-height: 95vh;
        flex-direction: column;
    }
    
    .lightbox-header {
        padding: 1rem;
    }
    
    .lightbox-title {
        font-size: 1.25rem;
    }
    
    .lightbox-body {
        flex-direction: column;
    }
    
    .lightbox-image-container {
        min-height: 250px;
    }
    
    .lightbox-metadata {
        width: 100%;
        border-left: none;
        border-top: 1px solid #eee;
        padding: 1rem;
    }
    
    .lightbox-nav-btn {
        width: 40px;
        height: 40px;
    }
    
    .lightbox-nav-btn.prev {
        left: 0.5rem;
    }
    
    .lightbox-nav-btn.next {
        right: 0.5rem;
    }
}

/* Very small mobile (480px and below) */
@media (max-width: 480px) {
    .card-image {
        height: 180px;
    }
    
    .card-overlay .cat-name {
        font-size: 0.95rem;
    }
    
    .card-expand-btn {
        width: 40px;
        height: 40px;
    }
    
    .carousel-nav {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }
    
    .carousel-nav.prev {
        left: -16px;
    }
    
    .carousel-nav.next {
        right: -16px;
    }
}

/* --------------------------------------------------
 * Print Styles
 * -------------------------------------------------- */
@media print {
    .modern-carousel {
        break-inside: avoid;
    }
    
    .carousel-nav,
    .carousel-indicators,
    .cat-lightbox {
        display: none !important;
    }
    
    .cat-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .cat-card:hover {
        transform: none;
    }
}

/* --------------------------------------------------
 * High Contrast Mode Support
 * -------------------------------------------------- */
@media (prefers-contrast: high) {
    .cat-card {
        border: 2px solid #000;
    }
    
    .carousel-nav {
        border: 2px solid #000;
        background: #2d2d2d;
        color: #000;
    }
    
    .card-overlay {
        background: rgba(0, 0, 0, 0.9);
    }
}

/* --------------------------------------------------
 * Reduced Motion Support
 * -------------------------------------------------- */
@media (prefers-reduced-motion: reduce) {
    .cat-card,
    .carousel-nav,
    .carousel-track,
    .card-image img,
    .card-overlay,
    .card-hover-overlay,
    .cat-lightbox {
        transition: none !important;
        animation: none !important;
    }
    
    .spinner {
        animation: none !important;
        border-top-color: transparent;
    }
}
