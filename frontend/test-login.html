<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Login</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; }
        input { padding: 8px; width: 200px; }
        button { padding: 10px 20px; margin: 10px 0; }
        .result { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <h1>Test Admin Login</h1>
    
    <form id="test-form">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" value="admin">
        </div>
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="yendorian.space-xyz">
        </div>
        <button type="submit">Test Login</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        const form = document.getElementById('test-form');
        const resultDiv = document.getElementById('result');
        
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            resultDiv.innerHTML = 'Testing login...';
            resultDiv.className = 'result';
            
            try {
                console.log('Attempting login to /api/AdminAuth/login');
                
                const response = await fetch('/api/AdminAuth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password }),
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('Login successful:', data);

                    // Store token and redirect like the real admin page
                    localStorage.setItem('admin_token', data.token);

                    resultDiv.innerHTML = `
                        <h3>Login Successful!</h3>
                        <p><strong>Username:</strong> ${data.user.username}</p>
                        <p><strong>Email:</strong> ${data.user.email}</p>
                        <p><strong>Role:</strong> ${data.user.role}</p>
                        <p><strong>Token:</strong> ${data.token.substring(0, 50)}...</p>
                        <p><strong>Must Change Password:</strong> ${data.mustChangePassword}</p>
                        <p><strong>Redirecting to upload page in 3 seconds...</strong></p>
                    `;
                    resultDiv.className = 'result success';

                    // Redirect after 3 seconds
                    setTimeout(() => {
                        window.location.href = 'upload.html';
                    }, 3000);
                } else {
                    const errorText = await response.text();
                    console.error('Login failed:', response.status, errorText);
                    
                    resultDiv.innerHTML = `
                        <h3>Login Failed</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Error:</strong> ${errorText}</p>
                    `;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                console.error('Network error:', error);
                
                resultDiv.innerHTML = `
                    <h3>Network Error</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p>Check the browser console for more details.</p>
                `;
                resultDiv.className = 'result error';
            }
        });
    </script>
</body>
</html>
