<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery V2 Integration Test - Yendor Cats</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/variables.css">
    <link rel="stylesheet" href="css/gallery-v2.css">
    <link rel="stylesheet" href="css/gallery-v2-integration.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #ffffff;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .test-section {
            margin-bottom: 40px;
        }
        
        .test-section h3 {
            color: #ffffff;
            margin-bottom: 15px;
            padding: 10px;
            background: #404040;
            border-left: 4px solid #007bff;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status-loading {
            background: #ffc107;
            color: #856404;
        }
        
        .status-success {
            background: #28a745;
            color: white;
        }
        
        .status-error {
            background: #dc3545;
            color: white;
        }
        
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .api-test-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .api-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
        }
        
        .api-result h5 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        
        .api-result .result-status {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .api-result .result-details {
            font-size: 0.8rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 Gallery V2 Integration Test</h1>
            <p>Testing the integration of Gallery V2 system with Backblaze B2 storage</p>
        </div>

        <!-- API Connectivity Test -->
        <div class="test-section">
            <h3>🔌 API Connectivity Test <span id="api-status" class="status-indicator status-loading">Testing...</span></h3>
            <p>Testing connectivity to Gallery V2 API endpoints</p>
            <div class="api-test-results" id="api-results">
                <!-- Results will be populated by JavaScript -->
            </div>
        </div>

        <!-- Gallery Loading Test -->
        <div class="test-section">
            <h3>📸 Gallery Loading Test <span id="gallery-status" class="status-indicator status-loading">Loading...</span></h3>
            <p>Testing image loading from Backblaze storage for each category</p>
            
            <!-- Kittens Test -->
            <div class="gallery-v2-container" data-category="kittens">
                <h4>Kittens Gallery</h4>
                <div class="gallery-grid-v2" id="test-kittens-gallery">
                    <div class="loading-placeholder" id="test-kittens-loading">
                        <div class="loading-spinner">
                            <div class="spinner"></div>
                        </div>
                        <p>Loading kittens...</p>
                        <small>Testing Backblaze B2 connectivity</small>
                    </div>
                </div>
            </div>

            <!-- Studs Test -->
            <div class="gallery-v2-container" data-category="studs">
                <h4>Studs Gallery</h4>
                <div class="gallery-grid-v2" id="test-studs-gallery">
                    <div class="loading-placeholder" id="test-studs-loading">
                        <div class="loading-spinner">
                            <div class="spinner"></div>
                        </div>
                        <p>Loading studs...</p>
                        <small>Testing Backblaze B2 connectivity</small>
                    </div>
                </div>
            </div>

            <!-- Queens Test -->
            <div class="gallery-v2-container" data-category="queens">
                <h4>Queens Gallery</h4>
                <div class="gallery-grid-v2" id="test-queens-gallery">
                    <div class="loading-placeholder" id="test-queens-loading">
                        <div class="loading-spinner">
                            <div class="spinner"></div>
                        </div>
                        <p>Loading queens...</p>
                        <small>Testing Backblaze B2 connectivity</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Debug Information -->
        <div class="test-section">
            <h3>🔍 Debug Information</h3>
            <div class="debug-info" id="debug-info">
                <strong>Test Status:</strong> Initializing...<br>
                <strong>Browser:</strong> <span id="browser-info"></span><br>
                <strong>Timestamp:</strong> <span id="timestamp"></span><br>
                <strong>Performance:</strong> <span id="performance-info">Measuring...</span>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/gallery-v2.js"></script>
    <script>
        class GalleryV2Test {
            constructor() {
                this.testResults = {
                    apiConnectivity: {},
                    galleryLoading: {},
                    imageLoading: {}
                };
                this.startTime = performance.now();
                this.init();
            }

            init() {
                console.log('🧪 Starting Gallery V2 Integration Test');
                this.updateDebugInfo();
                this.testApiConnectivity();
                this.testGalleryLoading();
            }

            updateDebugInfo() {
                document.getElementById('browser-info').textContent = navigator.userAgent.split(' ').pop();
                document.getElementById('timestamp').textContent = new Date().toISOString();
                
                setInterval(() => {
                    const elapsed = ((performance.now() - this.startTime) / 1000).toFixed(1);
                    document.getElementById('performance-info').textContent = `${elapsed}s elapsed`;
                }, 1000);
            }

            async testApiConnectivity() {
                const categories = ['kittens', 'studs', 'queens', 'gallery'];
                const resultsContainer = document.getElementById('api-results');
                
                for (const category of categories) {
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'api-result';
                    resultDiv.innerHTML = `
                        <h5>${category.charAt(0).toUpperCase() + category.slice(1)}</h5>
                        <div class="result-status">Testing...</div>
                        <div class="result-details">Connecting to API...</div>
                    `;
                    resultsContainer.appendChild(resultDiv);

                    try {
                        const startTime = performance.now();
                        const response = await fetch(`/api/v2/gallery/${category}?page=1&pageSize=5`);
                        const endTime = performance.now();
                        const responseTime = Math.round(endTime - startTime);

                        if (response.ok) {
                            const data = await response.json();
                            resultDiv.querySelector('.result-status').textContent = '✅ Success';
                            resultDiv.querySelector('.result-status').style.color = '#28a745';
                            resultDiv.querySelector('.result-details').innerHTML = `
                                Response: ${responseTime}ms<br>
                                Items: ${data.items?.length || 0}<br>
                                Cache: ${response.headers.get('X-Cache-Source') || 'unknown'}
                            `;
                            this.testResults.apiConnectivity[category] = { success: true, responseTime, data };
                        } else {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                    } catch (error) {
                        resultDiv.querySelector('.result-status').textContent = '❌ Failed';
                        resultDiv.querySelector('.result-status').style.color = '#dc3545';
                        resultDiv.querySelector('.result-details').textContent = error.message;
                        this.testResults.apiConnectivity[category] = { success: false, error: error.message };
                    }
                }

                this.updateApiStatus();
            }

            async testGalleryLoading() {
                const categories = ['kittens', 'studs', 'queens'];
                
                for (const category of categories) {
                    try {
                        console.log(`🔄 Testing ${category} gallery loading`);
                        
                        const gallery = new GalleryV2({
                            baseUrl: '/api/v2/gallery',
                            fallbackUrl: '/api/PublicGallery/category',
                            enablePerformanceMonitoring: true
                        });

                        const data = await gallery.loadCategory(category, 1, 5);
                        
                        if (data && data.items) {
                            this.renderTestGallery(category, data);
                            this.testResults.galleryLoading[category] = { success: true, itemCount: data.items.length };
                        } else {
                            throw new Error('No data returned');
                        }
                    } catch (error) {
                        console.error(`❌ ${category} gallery test failed:`, error);
                        this.showTestError(category, error.message);
                        this.testResults.galleryLoading[category] = { success: false, error: error.message };
                    }
                }

                this.updateGalleryStatus();
            }

            renderTestGallery(category, data) {
                const container = document.getElementById(`test-${category}-gallery`);
                const loading = document.getElementById(`test-${category}-loading`);
                
                if (loading) loading.style.display = 'none';
                
                if (!data.items || data.items.length === 0) {
                    container.innerHTML = '<p>No images found for this category.</p>';
                    return;
                }

                // Clear container and add items
                container.innerHTML = '';
                
                data.items.slice(0, 3).forEach((item, index) => {
                    const article = document.createElement('article');
                    article.className = 'gallery-item-v2';
                    article.innerHTML = `
                        <div class="gallery-item-media">
                            <img 
                                class="gallery-image" 
                                src="${item.imageUrl || item.publicUrl}"
                                alt="${item.title || item.catName || 'Cat photo'}"
                                style="aspect-ratio: ${item.aspectRatio || 1}; width: 100%; height: auto;"
                                onload="console.log('✅ Image loaded:', this.src)"
                                onerror="console.error('❌ Image failed:', this.src); this.style.background='#f8f9fa'; this.alt='Failed to load';"
                            />
                        </div>
                        <div class="gallery-item-details">
                            <h3 class="gallery-item-title">${item.title || item.catName || 'Maine Coon Cat'}</h3>
                            <div class="gallery-item-meta">
                                <span>Test Image ${index + 1}</span>
                            </div>
                        </div>
                    `;
                    container.appendChild(article);
                });

                console.log(`✅ ${category} gallery rendered with ${data.items.length} items`);
            }

            showTestError(category, message) {
                const container = document.getElementById(`test-${category}-gallery`);
                const loading = document.getElementById(`test-${category}-loading`);
                
                if (loading) loading.style.display = 'none';
                
                container.innerHTML = `
                    <div class="gallery-error">
                        <div class="error-icon">⚠️</div>
                        <div class="error-message">Test failed: ${message}</div>
                    </div>
                `;
            }

            updateApiStatus() {
                const statusEl = document.getElementById('api-status');
                const results = this.testResults.apiConnectivity;
                const successCount = Object.values(results).filter(r => r.success).length;
                const totalCount = Object.keys(results).length;

                if (successCount === totalCount) {
                    statusEl.textContent = `✅ All ${totalCount} APIs working`;
                    statusEl.className = 'status-indicator status-success';
                } else {
                    statusEl.textContent = `⚠️ ${successCount}/${totalCount} APIs working`;
                    statusEl.className = 'status-indicator status-error';
                }
            }

            updateGalleryStatus() {
                const statusEl = document.getElementById('gallery-status');
                const results = this.testResults.galleryLoading;
                const successCount = Object.values(results).filter(r => r.success).length;
                const totalCount = Object.keys(results).length;

                if (successCount === totalCount) {
                    statusEl.textContent = `✅ All ${totalCount} galleries loaded`;
                    statusEl.className = 'status-indicator status-success';
                } else {
                    statusEl.textContent = `⚠️ ${successCount}/${totalCount} galleries loaded`;
                    statusEl.className = 'status-indicator status-error';
                }
            }
        }

        // Start test when page loads
        document.addEventListener('DOMContentLoaded', () => {
            window.galleryTest = new GalleryV2Test();
        });
    </script>
</body>
</html>
