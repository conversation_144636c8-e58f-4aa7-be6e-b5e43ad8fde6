events {
    worker_connections 1024;
}

http {
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # MIME types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Trust Cloudflare IPs for real IP detection
    set_real_ip_from ************/20;
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from *************/18;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from **********/13;
    set_real_ip_from **********/22;
    real_ip_header CF-Connecting-IP;

    server {
        listen 80;
        server_name _;

        root /usr/share/nginx/html;
        index index.html;

        # Security headers (Cloudflare will add more)
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;

        # Static assets with aggressive caching
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary "Accept-Encoding";

            # CORS for fonts and assets
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, OPTIONS";
            add_header Access-Control-Allow-Headers "Range";
        }

        # HTML files - short cache for updates
        location ~* \.(html|htm)$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
            add_header Vary "Accept-Encoding";
        }

        # API proxy with optimized headers
        location /api/ {
            proxy_pass http://api:80/;

            # Essential proxy headers
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;

            # Performance optimizations
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            proxy_busy_buffers_size 8k;

            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;

            # Disable caching for API responses (let Cloudflare handle it)
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # Main SPA route
        location / {
            try_files $uri $uri/ /index.html;

            # Cache control for SPA
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
