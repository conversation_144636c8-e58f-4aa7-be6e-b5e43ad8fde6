# Frontend Dockerfile for YendorCats
# Simple nginx-based image for static content

FROM nginx:alpine AS production

# Remove default nginx website
RUN rm -rf /usr/share/nginx/html/*

# Copy frontend files to nginx html directory
COPY . /usr/share/nginx/html
# --- Cache-busting stamping ---
# We stamp __ASSET_VERSION__ placeholder in HTML with a build-time version (git SHA or provided arg).
# This ensures static assets are cached aggressively (immutable) while still updating on deploys.
# Usage:
#   docker build -t your-frontend:TAG -f frontend/Dockerfile frontend \
#     --build-arg BUILD_VERSION=$(git rev-parse --short HEAD)
# If BUILD_VERSION not provided, defaults to 'dev'.
ARG BUILD_VERSION=dev
ENV ASSET_VERSION=$BUILD_VERSION

# Replace placeholders in all HTML files
RUN find /usr/share/nginx/html -name "*.html" -type f -print0 | xargs -0 -I {} sh -c 'sed -i "s/__ASSET_VERSION__/${ASSET_VERSION}/g" "{}"'

# Remove Dockerfile and nginx.conf from html directory
RUN rm -f /usr/share/nginx/html/Dockerfile /usr/share/nginx/html/nginx.conf

# Copy custom nginx configuration based on environment
# Use production config by default, can be overridden with build args
ARG NGINX_CONFIG=production
COPY nginx.development.conf /etc/nginx/nginx.conf.new
RUN cp /etc/nginx/nginx.conf.new /etc/nginx/nginx.conf

# Remove default nginx configuration that might interfere
RUN rm -f /etc/nginx/conf.d/default.conf /etc/nginx/conf.d/*.conf

# Create directory for nginx logs
RUN mkdir -p /var/log/nginx

# Set proper permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

# Expose port 80
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
