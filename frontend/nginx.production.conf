events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logging
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log warn;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        image/svg+xml;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=upload:10m rate=5r/s;
    
    # Upstream definitions for better load balancing and health checks
    upstream api_backend {
        server api:80 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    upstream uploader_backend {
        server uploader:80 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        root /usr/share/nginx/html;
        index index.html;
        
        # Security headers (CSP handled by .NET middleware)
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        
        # Static assets with long cache
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Access-Control-Allow-Origin "*";
            
            # Serve files directly
            try_files $uri =404;
        }
        
        # API proxy to backend service with improved error handling
        location /api/ {
            # Rate limiting for API calls
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://api_backend/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Connection pooling
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            # Timeout settings
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
            
            # Retry logic
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 2;
            proxy_next_upstream_timeout 30s;
            
            # CORS headers for API requests
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
            add_header Access-Control-Allow-Credentials "true" always;
            
            # Handle preflight requests
            if ($request_method = OPTIONS) {
                add_header Access-Control-Allow-Origin "*";
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
                add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With";
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
        }
        
        # File uploader service proxy
        location /uploader/ {
            # Rate limiting for uploads
            limit_req zone=upload burst=10 nodelay;
            
            proxy_pass http://uploader_backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Connection pooling
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            # Increased timeouts for file uploads
            proxy_connect_timeout 60s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            
            # Increase max body size for file uploads
            client_max_body_size 50M;
            
            # CORS headers
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
            
            # Handle preflight requests
            if ($request_method = OPTIONS) {
                add_header Access-Control-Allow-Origin "*";
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
                add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With";
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
        }
        
        # Main application routes (SPA fallback)
        location / {
            try_files $uri $uri/ /index.html;
            
            # Cache control for HTML files
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
            
            # CORS headers for main app
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
        }
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Debug endpoint to check backend connectivity
        location /debug/api-health {
            access_log off;
            proxy_pass http://api_backend/health;
            proxy_set_header Host $host;
            add_header Content-Type text/plain;
        }
        
        # Debug endpoint to check uploader connectivity  
        location /debug/uploader-health {
            access_log off;
            proxy_pass http://uploader_backend/health;
            proxy_set_header Host $host;
            add_header Content-Type text/plain;
        }
        
        # Robots.txt
        location /robots.txt {
            add_header Content-Type text/plain;
            return 200 "User-agent: *\nDisallow: /api/\nDisallow: /uploader/\n";
        }
    }
}
