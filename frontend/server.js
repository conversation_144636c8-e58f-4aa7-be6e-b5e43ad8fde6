const express = require('express');
const path = require('path');
const app = express();
const port = 3000;

// Security headers middleware
app.use((req, res, next) => {
  res.setHeader('Content-Security-Policy', "default-src 'self'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; script-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:;");
  next();
});

app.use(express.static(path.join(__dirname, '')));

app.listen(port, () => {
  console.log(`Server listening at http://localhost:${port}`);
});
