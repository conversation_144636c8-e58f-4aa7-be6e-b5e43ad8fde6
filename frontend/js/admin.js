document.addEventListener('DOMContentLoaded', () => {
    const loginForm = document.getElementById('login-form');
    const adminPanel = document.getElementById('admin-panel');
    const adminContent = document.getElementById('admin-content');
    const adminUsername = document.getElementById('admin-username');
    const logoutBtn = document.getElementById('logout-btn');
    const apiBaseUrl = '/api/AdminAuth';
    const adminApiBaseUrl = '/api/Admin';
    const catManagementApiBaseUrl = '/api/CatManagement';

    // Check for existing token
    const token = localStorage.getItem('admin_token');
    if (token) {
        // If user already has a token, redirect to upload page
        window.location.href = 'upload.html';
    }

    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const username = e.target.username.value;
        const password = e.target.password.value;

        try {
            const response = await fetch(`${apiBaseUrl}/login`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ username, password }),
            });

            if (response.ok) {
                const data = await response.json();
                localStorage.setItem('admin_token', data.token);
                // Redirect to upload page after successful login
                window.location.href = 'upload.html';
            } else {
                alert('Login failed. Please check your credentials.');
            }
        } catch (error) {
            console.error('Login error:', error);
            alert('An error occurred during login.');
        }
    });

    logoutBtn.addEventListener('click', () => {
        localStorage.removeItem('admin_token');
        adminPanel.style.display = 'none';
        loginForm.style.display = 'block';
        adminContent.innerHTML = '';
    });

    async function showAdminPanel(token) {
        loginForm.style.display = 'none';
        adminPanel.style.display = 'block';

        try {
            const response = await fetch(`${apiBaseUrl}/me`, {
                headers: { Authorization: `Bearer ${token}` },
            });

            if (response.ok) {
                const user = await response.json();
                adminUsername.textContent = user.username;
                // Load the admin dashboard
                adminContent.innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        <h3>Welcome to the Admin Panel</h3>
                        <p>Manage your cattery metadata, photos, and newsletter subscriptions.</p>
                        
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0;">
                            <a href="admin-metadata-editor.html" class="btn btn-primary admin-card" style="
                                display: block; 
                                padding: 20px; 
                                background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); 
                                color: white; 
                                text-decoration: none; 
                                border-radius: 10px; 
                                font-weight: bold;
                                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                                transition: transform 0.2s ease;">
                                📝 Metadata Editor
                                <small style="display: block; margin-top: 8px; opacity: 0.9;">Manage cat photos and metadata</small>
                            </a>
                            
                            <button id="newsletter-manager-btn" class="btn btn-secondary admin-card" style="
                                padding: 20px; 
                                background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); 
                                color: white; 
                                border: none;
                                border-radius: 10px; 
                                font-weight: bold;
                                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                                transition: transform 0.2s ease;
                                cursor: pointer;">
                                📧 Newsletter Subscriptions
                                <small style="display: block; margin-top: 8px; opacity: 0.9;">View and manage subscribers</small>
                            </button>
                        </div>
                        
                        <div style="margin-top: 30px;">
                            <h4>Quick Stats</h4>
                            <div id="admin-quick-stats">Loading cattery statistics...</div>
                        </div>
                        
                        <!-- Newsletter Management Section (Initially Hidden) -->
                        <div id="newsletter-management" style="display: none; margin-top: 30px; text-align: left;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <h3>Newsletter Subscriptions</h3>
                                <button id="close-newsletter-btn" style="
                                    background: #6c757d; 
                                    color: white; 
                                    border: none; 
                                    padding: 8px 16px; 
                                    border-radius: 5px; 
                                    cursor: pointer;">
                                    ← Back to Dashboard
                                </button>
                            </div>
                            
                            <div id="newsletter-stats" style="margin-bottom: 20px;">
                                Loading newsletter statistics...
                            </div>
                            
                            <div style="margin-bottom: 15px;">
                                <label for="status-filter" style="margin-right: 10px;">Filter by Status:</label>
                                <select id="status-filter" style="
                                    padding: 8px 12px; 
                                    border: 1px solid #ddd; 
                                    border-radius: 4px; 
                                    margin-right: 10px;">
                                    <option value="">All Statuses</option>
                                    <option value="new">New</option>
                                    <option value="contacted">Contacted</option>
                                    <option value="followed-up">Followed Up</option>
                                    <option value="not-interested">Not Interested</option>
                                </select>
                                <button id="refresh-subscriptions" style="
                                    padding: 8px 16px; 
                                    background: #28a745; 
                                    color: white; 
                                    border: none; 
                                    border-radius: 4px; 
                                    cursor: pointer;">
                                    Refresh
                                </button>
                            </div>
                            
                            <div id="newsletter-subscriptions" style="
                                border: 1px solid #ddd; 
                                border-radius: 8px; 
                                background: white; 
                                min-height: 200px;">
                                Loading subscriptions...
                            </div>
                        </div>
                    </div>
                `;
                loadQuickStats();
                setupNewsletterManagement();
            } else {
                // Token might be invalid, so log out
                logoutBtn.click();
            }
        } catch (error) {
            console.error('Failed to fetch user info:', error);
            logoutBtn.click();
        }
    }

    async function loadQuickStats() {
        try {
            const token = localStorage.getItem('admin_token');
            const response = await fetch(`${adminApiBaseUrl}/cats/list-all`, {
                headers: { 
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                const statsHtml = `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; max-width: 600px; margin: 0 auto;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 1.5em; font-weight: bold;">${data.totalCats || 0}</div>
                            <div style="font-size: 0.9em;">Total Cats</div>
                        </div>
                        <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 1.5em; font-weight: bold;">${data.totalPhotos || 0}</div>
                            <div style="font-size: 0.9em;">Total Photos</div>
                        </div>
                        <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 1.5em; font-weight: bold;">${data.cats?.filter(cat => cat.breedingStatus === 'available-kitten').length || 0}</div>
                            <div style="font-size: 0.9em;">Available Kittens</div>
                        </div>
                        <div style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 1.5em; font-weight: bold;">${[...new Set(data.cats?.map(cat => cat.bloodline).filter(Boolean))].length || 0}</div>
                            <div style="font-size: 0.9em;">Bloodlines</div>
                        </div>
                    </div>
                `;
                document.getElementById('admin-quick-stats').innerHTML = statsHtml;
            } else {
                document.getElementById('admin-quick-stats').innerHTML = '<p>Unable to load statistics</p>';
            }
        } catch (error) {
            console.error('Error loading quick stats:', error);
            document.getElementById('admin-quick-stats').innerHTML = '<p>Error loading statistics</p>';
        }
    }

    function setupNewsletterManagement() {
        const newsletterBtn = document.getElementById('newsletter-manager-btn');
        const newsletterSection = document.getElementById('newsletter-management');
        const closeNewsletterBtn = document.getElementById('close-newsletter-btn');
        const statusFilter = document.getElementById('status-filter');
        const refreshBtn = document.getElementById('refresh-subscriptions');

        if (!newsletterBtn) return;

        newsletterBtn.addEventListener('click', () => {
            newsletterSection.style.display = 'block';
            loadNewsletterSubscriptions();
        });

        if (closeNewsletterBtn) {
            closeNewsletterBtn.addEventListener('click', () => {
                newsletterSection.style.display = 'none';
            });
        }

        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                loadNewsletterSubscriptions(statusFilter.value);
            });
        }

        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                loadNewsletterSubscriptions(statusFilter?.value || '');
            });
        }
    }

    async function loadNewsletterSubscriptions(statusFilter = '') {
        try {
            const token = localStorage.getItem('admin_token');
            if (!token) {
                console.error('No admin token found');
                return;
            }

            let url = '/api/newsletter/admin/subscriptions';
            if (statusFilter) {
                url = `/api/newsletter/admin/subscriptions/status/${encodeURIComponent(statusFilter)}`;
            }

            const response = await fetch(url, {
                headers: { 
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.success) {
                displayNewsletterStats(data);
                displayNewsletterSubscriptions(data.subscriptions);
            } else {
                throw new Error(data.message || 'Failed to load newsletter subscriptions');
            }
        } catch (error) {
            console.error('Error loading newsletter subscriptions:', error);
            document.getElementById('newsletter-stats').innerHTML = 
                '<p style="color: red;">Error loading newsletter statistics</p>';
            document.getElementById('newsletter-subscriptions').innerHTML = 
                '<p style="color: red; padding: 20px;">Error loading newsletter subscriptions</p>';
        }
    }

    function displayNewsletterStats(data) {
        const statsContainer = document.getElementById('newsletter-stats');
        if (!statsContainer) return;

        const totalSubscriptions = data.totalSubscriptions || 0;
        const statusSummary = data.statusSummary || {};
        const interestSummary = data.interestSummary || {};

        let statsHtml = `
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; margin-bottom: 20px;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5em; font-weight: bold;">${totalSubscriptions}</div>
                    <div style="font-size: 0.9em;">Total Subscribers</div>
                </div>
        `;

        // Add status summary cards
        Object.entries(statusSummary).forEach(([status, count]) => {
            const colors = {
                'new': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                'contacted': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                'followed-up': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                'not-interested': 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
            };
            
            const bgColor = colors[status] || 'linear-gradient(135deg, #a8a8a8 0%, #7d7d7d 100%)';
            const textColor = status === 'not-interested' ? '#333' : 'white';
            
            statsHtml += `
                <div style="background: ${bgColor}; color: ${textColor}; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 1.5em; font-weight: bold;">${count}</div>
                    <div style="font-size: 0.9em;">${status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}</div>
                </div>
            `;
        });

        statsHtml += '</div>';

        // Add interest summary if available
        if (Object.keys(interestSummary).length > 0) {
            statsHtml += `
                <div style="margin-top: 15px;">
                    <h4>Interest Areas:</h4>
                    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
            `;
            
            Object.entries(interestSummary).forEach(([interest, count]) => {
                statsHtml += `
                    <span style="
                        background: #e9ecef; 
                        padding: 5px 10px; 
                        border-radius: 15px; 
                        font-size: 0.9em;
                        border: 1px solid #dee2e6;">
                        ${interest}: ${count}
                    </span>
                `;
            });

            statsHtml += '</div></div>';
        }

        statsContainer.innerHTML = statsHtml;
    }

    function displayNewsletterSubscriptions(subscriptions) {
        const container = document.getElementById('newsletter-subscriptions');
        if (!container) return;

        if (!subscriptions || subscriptions.length === 0) {
            container.innerHTML = '<p style="padding: 20px; text-align: center;">No newsletter subscriptions found.</p>';
            return;
        }

        let tableHtml = `
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">Name</th>
                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">Email</th>
                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">Interests</th>
                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">Subscribed</th>
                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">Status</th>
                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">Actions</th>
                    </tr>
                </thead>
                <tbody>
        `;

        subscriptions.forEach(subscription => {
            const subscribedDate = new Date(subscription.subscribedAt).toLocaleDateString();
            const statusColors = {
                'new': '#007bff',
                'contacted': '#28a745',
                'followed-up': '#ffc107',
                'not-interested': '#6c757d'
            };
            
            const statusColor = statusColors[subscription.status] || '#6c757d';

            tableHtml += `
                <tr style="border-bottom: 1px solid #dee2e6;">
                    <td style="padding: 12px;">${subscription.name || 'N/A'}</td>
                    <td style="padding: 12px;">${subscription.email}</td>
                    <td style="padding: 12px;">${subscription.interests || 'Not specified'}</td>
                    <td style="padding: 12px; font-size: 0.9em;">${subscribedDate}</td>
                    <td style="padding: 12px;">
                        <span style="
                            background: ${statusColor}; 
                            color: white; 
                            padding: 4px 8px; 
                            border-radius: 12px; 
                            font-size: 0.8em; 
                            font-weight: bold;">
                            ${subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1).replace('-', ' ')}
                        </span>
                    </td>
                    <td style="padding: 12px;">
                        <select onchange="updateSubscriptionStatus('${subscription.id}', this.value)" style="
                            padding: 4px 8px; 
                            border: 1px solid #ddd; 
                            border-radius: 4px; 
                            font-size: 0.9em;">
                            <option value="new" ${subscription.status === 'new' ? 'selected' : ''}>New</option>
                            <option value="contacted" ${subscription.status === 'contacted' ? 'selected' : ''}>Contacted</option>
                            <option value="followed-up" ${subscription.status === 'followed-up' ? 'selected' : ''}>Followed Up</option>
                            <option value="not-interested" ${subscription.status === 'not-interested' ? 'selected' : ''}>Not Interested</option>
                        </select>
                    </td>
                </tr>
            `;
        });

        tableHtml += '</tbody></table>';
        container.innerHTML = tableHtml;
    }

    // Make updateSubscriptionStatus globally available
    window.updateSubscriptionStatus = async function(subscriptionId, newStatus) {
        try {
            const token = localStorage.getItem('admin_token');
            if (!token) {
                alert('Authentication required');
                return;
            }

            const response = await fetch(`/api/newsletter/admin/subscriptions/${subscriptionId}/status`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    status: newStatus,
                    notes: `Status updated to ${newStatus} via admin panel`
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            
            if (result.success) {
                console.log('Subscription status updated successfully');
                // Refresh the current filter
                const statusFilter = document.getElementById('status-filter');
                loadNewsletterSubscriptions(statusFilter?.value || '');
            } else {
                throw new Error(result.message || 'Failed to update subscription status');
            }
        } catch (error) {
            console.error('Error updating subscription status:', error);
            alert('Failed to update subscription status. Please try again.');
        }
    };
});
