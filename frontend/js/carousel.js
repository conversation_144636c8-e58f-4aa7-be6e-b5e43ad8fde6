/**
 * Carousel Component for Yendor Cats
 * Features:
 * - Auto-scrolling
 * - Manual navigation
 * - Progress indicator
 * - Supports both single and multi-item views
 * - Touch swipe support for mobile
 * - Lightbox/popup for images
 */

class Carousel {
    /**
     * Initialize a new carousel
     * @param {HTMLElement} element - The carousel element
     * @param {Object} options - Carousel options
     */
    constructor(element, options = {}) {
        // DOM Elements
        this.carousel = element;
        this.container = this.carousel.querySelector('.carousel-container');

        // Clear any existing slides with loading placeholder
        const loadingPlaceholder = this.container.querySelector('.loading-placeholder');
        if (loadingPlaceholder) {
            console.log('Removing loading placeholder from carousel');
            loadingPlaceholder.remove();
        }

        // Get updated slides collection after any dynamic content changes
        this.slides = this.carousel.querySelectorAll('.carousel-slide');
        console.log(`Carousel constructor found ${this.slides.length} slides`);

        // Default Options
        this.options = {
            autoplay: true,
            autoplaySpeed: 5000, // ms
            slideDuration: 500, // ms
            pauseOnHover: true,
            infinite: true,
            // Use a compact scrollbar instead of dot indicators by default
            indicators: false,
            scrollbar: true,
            navigation: true,
            ...options
        };

        // State
        this.currentIndex = 0;
        this.slideCount = this.slides.length;
        this.autoplayTimer = null;
        this.progressBar = null;
        this.isTransitioning = false;
        this.touchStartX = 0;
        this.touchStartY = 0;
        this.touchEndX = 0;
        this.touchEndY = 0;
        this.touchStartTime = 0;

        console.log(`Carousel constructor: slideCount = ${this.slideCount}`);

        // Initialize
        this.init();
    }

    /**
     * Initialize the carousel
     */
    init() {
        console.log('Initializing carousel with the following structure:');
        console.log('- Carousel element:', this.carousel);
        console.log('- Container element:', this.container);

        // Ensure slides are refreshed from DOM
        this.slides = this.carousel.querySelectorAll('.carousel-slide');
        this.slideCount = this.slides.length;

        console.log(`- Found ${this.slideCount} slides:`, this.slides);

        // Skip if no slides
        if (this.slideCount === 0) {
            console.error('❌ No slides found, carousel initialization aborted');
            return;
        }

        // Ensure container is displayed correctly
        if (this.container) {
            this.container.style.display = 'flex';
            this.container.style.transition = 'transform 0.5s ease';
            console.log('✓ Set container display to flex');
        }

        // Remove any existing controls
        this.removeExistingControls();
        console.log('✓ Removed any existing controls');

        // Always create navigation buttons - this was the issue
        this.createNavigation();
        console.log('✓ Created navigation buttons');

        // Create indicators if enabled
        if (this.options.indicators) {
            this.createIndicators();
            console.log('✓ Created indicators');
        }

        // Create progress bar for autoplay
        if (this.options.autoplay) {
            this.createProgressBar();
            console.log('✓ Created progress bar');
        }

        // Create compact scrollbar if enabled
        if (this.options.scrollbar) {
            this.createScrollbar();
            console.log('✓ Created compact scrollbar');
        }

        // Set up event listeners
        this.setupEventListeners();
        console.log('✓ Set up event listeners');

        // Reset to first slide
        this.currentIndex = 0;
        this.goToSlide(0);
        console.log('✓ Reset to first slide');

        // Start autoplay if enabled
        if (this.options.autoplay) {
            this.startAutoplay();
            console.log('✓ Started autoplay');
        }

        console.log('Carousel initialization complete');
    }

    /**
     * Remove existing navigation and indicators
     */
    removeExistingControls() {
        // Remove existing buttons
        const existingButtons = this.carousel.querySelectorAll('.carousel-button');
        existingButtons.forEach(button => button.remove());

        // Remove existing indicators
        const existingIndicators = this.carousel.querySelector('.carousel-indicators');
        if (existingIndicators) {
            existingIndicators.remove();
        }

        // Remove existing slide counter
        const existingSlideCounter = this.carousel.querySelector('.carousel-slide-counter');
        if (existingSlideCounter) {
            existingSlideCounter.remove();
        }

        // Remove existing progress bar
        const existingProgress = this.carousel.querySelector('.carousel-progress');
        if (existingProgress) {
            existingProgress.remove();
        }

        // Remove existing scrollbar
        const existingScrollbar = this.carousel.querySelector('.carousel-scrollbar');
        if (existingScrollbar) {
            existingScrollbar.remove();
        }
    }

    /**
     * Create navigation buttons
     */
    createNavigation() {
        // Create prev button
        const prevButton = document.createElement('button');
        prevButton.className = 'carousel-button prev';
        prevButton.setAttribute('aria-label', 'Previous slide');
        prevButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
        `;

        // Create next button
        const nextButton = document.createElement('button');
        nextButton.className = 'carousel-button next';
        nextButton.setAttribute('aria-label', 'Next slide');
        nextButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
        `;

        // Add buttons to carousel
        this.carousel.appendChild(prevButton);
        this.carousel.appendChild(nextButton);

        // Store references
        this.prevButton = prevButton;
        this.nextButton = nextButton;
    }

    /**
     * Create slide indicators
     */
    createIndicators() {
        const indicators = document.createElement('div');
        indicators.className = 'carousel-indicators';

        // Add a text indicator showing current slide / total slides
        const slideCounter = document.createElement('div');
        slideCounter.className = 'carousel-slide-counter';
        slideCounter.innerHTML = `<span>1</span>/<span>${this.slideCount}</span>`;
        this.carousel.appendChild(slideCounter);
        this.slideCounter = slideCounter;

        for (let i = 0; i < this.slideCount; i++) {
            const indicator = document.createElement('button');
            indicator.className = 'carousel-indicator';
            indicator.setAttribute('aria-label', `Go to slide ${i + 1}`);
            indicator.dataset.index = i;
            indicators.appendChild(indicator);
        }

        // Append indicators and cache refs
        this.carousel.appendChild(indicators);
        this.indicators = this.carousel.querySelectorAll('.carousel-indicator');
        this.updateIndicators();
    }


    /**
     * Create a compact horizontal scrollbar for slides
     */
    createScrollbar() {
        // Wrapper for scrollbar to avoid overlapping images
        const bar = document.createElement('div');
        bar.className = 'carousel-scrollbar';

        const track = document.createElement('div');
        track.className = 'carousel-scrollbar-track';

        const thumb = document.createElement('div');
        thumb.className = 'carousel-scrollbar-thumb';

        track.appendChild(thumb);
        bar.appendChild(track);
        this.carousel.appendChild(bar);

        this.scrollbar = { bar, track, thumb };
        this.updateScrollbar();

        // Drag to seek
        let isDragging = false;
        let startX = 0;
        let startLeft = 0;

        const onMove = (clientX) => {
            const delta = clientX - startX;
            const trackRect = track.getBoundingClientRect();
            const maxLeft = trackRect.width - thumb.offsetWidth;
            let nextLeft = Math.min(Math.max(0, startLeft + delta), maxLeft);
            const ratio = nextLeft / maxLeft;
            const targetIndex = Math.round(ratio * (this.slideCount - 1));
            if (targetIndex !== this.currentIndex) {
                this.goToSlide(targetIndex);
            }
            thumb.style.left = `${nextLeft}px`;
        };

        thumb.addEventListener('mousedown', (e) => {
            isDragging = true;
            startX = e.clientX;
            startLeft = parseFloat(window.getComputedStyle(thumb).left) || 0;
            document.body.classList.add('no-select');
            e.preventDefault();
        });
        window.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            onMove(e.clientX);
        });
        window.addEventListener('mouseup', () => {
            isDragging = false;
            document.body.classList.remove('no-select');
        });

        // Touch support
        thumb.addEventListener('touchstart', (e) => {
            isDragging = true;
            startX = e.touches[0].clientX;
            startLeft = parseFloat(window.getComputedStyle(thumb).left) || 0;
        }, { passive: true });
        window.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            onMove(e.touches[0].clientX);
        }, { passive: true });
        window.addEventListener('touchend', () => { isDragging = false; });
    }

    /**
     * Update scrollbar thumb size/position
     */
    updateScrollbar() {
        if (!this.scrollbar) return;
        const { track, thumb } = this.scrollbar;
        const ratio = 1 / this.slideCount;
        const trackWidth = track.clientWidth;
        const thumbWidth = Math.max(30, trackWidth * ratio);
        thumb.style.width = `${thumbWidth}px`;

        const maxLeft = trackWidth - thumbWidth;
        const left = maxLeft * (this.currentIndex / (this.slideCount - 1 || 1));
        thumb.style.left = `${left}px`;
    }

    /**
     * Create progress bar for autoplay
     */
    createProgressBar() {
        const progressBar = document.createElement('div');
        progressBar.className = 'carousel-progress';
        this.carousel.appendChild(progressBar);
        this.progressBar = progressBar;
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Navigation buttons - always add these listeners since we always create the buttons
        this.prevButton.addEventListener('click', () => this.prev());
        this.nextButton.addEventListener('click', () => this.next());

        // Indicators
        if (this.options.indicators && this.indicators) {
            this.indicators.forEach(indicator => {
                indicator.addEventListener('click', () => {
                    const index = parseInt(indicator.dataset.index);
                    this.goToSlide(index);
                });
            });
        }

        // Pause on hover
        if (this.options.pauseOnHover && this.options.autoplay) {
            this.carousel.addEventListener('mouseenter', () => this.pauseAutoplay());
            this.carousel.addEventListener('mouseleave', () => this.startAutoplay());
        }

        // Touch events for swipe
        // Window resize to handle responsive adjustments
        window.addEventListener('resize', () => {
            this.goToSlide(this.currentIndex);
            this.updateScrollbar();
        });
        this.carousel.addEventListener('touchstart', e => this.handleTouchStart(e), { passive: true });
        this.carousel.addEventListener('touchmove', e => this.handleTouchMove(e), { passive: true });
        this.carousel.addEventListener('touchend', () => this.handleTouchEnd());

        // Window resize to handle responsive adjustments
        window.addEventListener('resize', () => this.goToSlide(this.currentIndex));
    }

    /**
     * Handle touch start event
     * @param {TouchEvent} e - Touch event
     */
    handleTouchStart(e) {
        const t = e.touches[0];
        this.touchStartX = t.clientX;
        this.touchStartY = t.clientY;
        this.touchStartTime = Date.now();
        this.pauseAutoplay();
    }

    /**
     * Handle touch move event
     * @param {TouchEvent} e - Touch event
     */
    handleTouchMove(e) {
        const t = e.touches ? e.touches[0] : e;
        if (t) {
            this.touchEndX = t.clientX;
            this.touchEndY = t.clientY ?? this.touchEndY;
        }
    }

    /**
     * Handle touch end event
     */
    handleTouchEnd() {
        const dx = (this.touchStartX ?? 0) - (this.touchEndX ?? this.touchStartX ?? 0);
        const dy = (this.touchStartY ?? 0) - (this.touchEndY ?? this.touchStartY ?? 0);
        const dt = Date.now() - (this.touchStartTime || Date.now());
        const absDx = Math.abs(dx);
        const absDy = Math.abs(dy);

        // If the gesture was mostly vertical or very short, treat it as a tap
        const TAP_DISTANCE = 8; // px
        const SWIPE_THRESHOLD = 50; // px
        const TAP_TIME = 250; // ms

        const isTap = absDx < TAP_DISTANCE && absDy < TAP_DISTANCE && dt < TAP_TIME;
        const isHorizontalSwipe = absDx > SWIPE_THRESHOLD && absDx > absDy;

        if (isTap) {
            // Simulate a click on the target element so images open easily on mobile
            const target = document.elementFromPoint(this.touchEndX || this.touchStartX, this.touchEndY || this.touchStartY);
            if (target) target.click();
            return;
        }

        if (isHorizontalSwipe) {
            if (dx > 0) {
                this.next();
            } else {
                this.prev();
            }
        }

        if (this.options.autoplay) {
            this.startAutoplay();
        }
    }

    /**
     * Go to previous slide
     */
    prev() {
        if (this.isTransitioning) return;

        let prevIndex = this.currentIndex - 1;
        if (prevIndex < 0) {
            prevIndex = this.options.infinite ? this.slideCount - 1 : 0;
        }

        this.goToSlide(prevIndex);
    }

    /**
     * Go to next slide
     */
    next() {
        if (this.isTransitioning) return;

        let nextIndex = this.currentIndex + 1;
        if (nextIndex >= this.slideCount) {
            nextIndex = this.options.infinite ? 0 : this.slideCount - 1;
        }
        // Keep scrollbar in sync on slide changes and resizes
        this.updateScrollbar();

        this.goToSlide(nextIndex);
    }

    /**
     * Go to specific slide
     * @param {number} index - Slide index
     */
    goToSlide(index) {
        if (this.isTransitioning) return;

        // Ensure valid index
        if (index < 0) index = 0;
        if (index >= this.slideCount) index = this.slideCount - 1;

        this.isTransitioning = true;

        // Update current index
        // Update scrollbar position
        this.updateScrollbar();
        this.currentIndex = index;

        // Get width of the carousel
        const carouselWidth = this.carousel.offsetWidth;
        const position = -index * carouselWidth;

        console.log(`Going to slide ${index + 1}/${this.slideCount}, position: ${position}px, carousel width: ${carouselWidth}px`);

        // Update transform position
        this.container.style.transform = `translateX(${position}px)`;

        // Update indicators
        this.updateIndicators();

        // Reset autoplay
        if (this.options.autoplay) {
            this.resetAutoplay();
        }

        // Clear transitioning state after animation completes
        setTimeout(() => {
            this.isTransitioning = false;
        }, this.options.slideDuration);
    }

    /**
     * Update indicators
     */
    updateIndicators() {
        if (!this.options.indicators) return;

        this.indicators.forEach((indicator, index) => {
            if (index === this.currentIndex) {
                indicator.classList.add('active');
            } else {
                indicator.classList.remove('active');
            }
        });

        // Update the slide counter
        if (this.slideCounter) {
            this.slideCounter.innerHTML = `<span>${this.currentIndex + 1}</span>/<span>${this.slideCount}</span>`;
        }
    }

    /**
     * Start autoplay
     */
    startAutoplay() {
        if (!this.options.autoplay) return;

        clearInterval(this.autoplayTimer);
        this.autoplayTimer = setInterval(() => {
            this.next();
        }, this.options.autoplaySpeed);

        this.startProgressBar();
    }

    /**
     * Pause autoplay
     */
    pauseAutoplay() {
        clearInterval(this.autoplayTimer);
        this.stopProgressBar();
    }

    /**
     * Reset autoplay timer
     */
    resetAutoplay() {
        this.pauseAutoplay();
        this.startAutoplay();
    }

    /**
     * Start progress bar animation
     */
    startProgressBar() {
        if (!this.progressBar) return;

        this.progressBar.style.width = '0%';
        this.progressBar.style.transition = `width ${this.options.autoplaySpeed}ms linear`;

        // Force reflow
        this.progressBar.offsetHeight;

        this.progressBar.style.width = '100%';
    }

    /**
     * Stop progress bar animation
     */
    stopProgressBar() {
        if (!this.progressBar) return;

        const computedStyle = window.getComputedStyle(this.progressBar);
        const width = parseFloat(computedStyle.getPropertyValue('width'));
        const totalWidth = parseFloat(computedStyle.getPropertyValue('width'));

        this.progressBar.style.transition = 'none';
        this.progressBar.style.width = `${width}px`;
    }

    /**
     * Setup lightbox/popup for images
     */
    setupLightbox() {
        // Find all images in carousel
        const carouselImages = this.carousel.querySelectorAll('img');

        // Skip if no images
        if (carouselImages.length === 0) return;

        // Create lightbox if it doesn't exist yet
        if (!document.querySelector('.lightbox')) {
            this.createLightbox();
        }

        // Get lightbox elements
        const lightbox = document.querySelector('.lightbox');
        const lightboxImage = lightbox.querySelector('.lightbox-image');
        const closeButton = lightbox.querySelector('.lightbox-close');
        const prevButton = lightbox.querySelector('.lightbox-nav-button.prev');
        const nextButton = lightbox.querySelector('.lightbox-nav-button.next');

        // Current image index for lightbox
        let currentLightboxIndex = 0;

        // All images on the page for lightbox navigation (not just this carousel)
        const allImages = Array.from(document.querySelectorAll('.carousel img'));

        // Add click event to each image
        carouselImages.forEach((img) => {
            // Make the image itself easy to tap on mobile by stopping swipe if it's a tap
            img.addEventListener('click', (e) => {
                e.stopPropagation();
                const globalIndex = allImages.indexOf(img);
                currentLightboxIndex = globalIndex;
                lightboxImage.src = img.src;
                lightbox.classList.add('active');
                document.body.style.overflow = 'hidden';
            });
        });

        // Close lightbox on click
        closeButton.addEventListener('click', () => {
            lightbox.classList.remove('active');

            // Enable page scrolling
            document.body.style.overflow = '';
        });

        // Navigate to previous image
        prevButton.addEventListener('click', () => {
            currentLightboxIndex = (currentLightboxIndex - 1 + allImages.length) % allImages.length;
            lightboxImage.src = allImages[currentLightboxIndex].src;
        });

        // Navigate to next image
        nextButton.addEventListener('click', () => {
            currentLightboxIndex = (currentLightboxIndex + 1) % allImages.length;
            lightboxImage.src = allImages[currentLightboxIndex].src;
        });

        // Close lightbox on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            } else if (e.key === 'ArrowLeft') {
                if (lightbox.classList.contains('active')) {
                    currentLightboxIndex = (currentLightboxIndex - 1 + allImages.length) % allImages.length;
                    lightboxImage.src = allImages[currentLightboxIndex].src;
                }
            } else if (e.key === 'ArrowRight') {
                if (lightbox.classList.contains('active')) {
                    currentLightboxIndex = (currentLightboxIndex + 1) % allImages.length;
                    lightboxImage.src = allImages[currentLightboxIndex].src;
                }
            }
        });

        // Close lightbox when clicking outside the image
        lightbox.addEventListener('click', (e) => {
            if (e.target === lightbox) {
                lightbox.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }

    /**
     * Create lightbox/popup for images
     */
    createLightbox() {
        const lightbox = document.createElement('div');
        lightbox.className = 'lightbox';

        lightbox.innerHTML = `
            <div class="lightbox-content">
                <img class="lightbox-image" src="" alt="Enlarged view">
                <button class="lightbox-close" aria-label="Close lightbox">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="lightbox-nav">
                <button class="lightbox-nav-button prev" aria-label="Previous image">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="15 18 9 12 15 6"></polyline>
                    </svg>
                </button>
                <button class="lightbox-nav-button next" aria-label="Next image">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                </button>
            </div>
        `;

        document.body.appendChild(lightbox);
    }

    /**
     * Force refresh the carousel with the current DOM state
     * This can be called after dynamically adding/removing slides
     */
    forceRefresh() {
        console.log('Force refreshing carousel');

        // Re-query all slides
        this.slides = this.carousel.querySelectorAll('.carousel-slide');
        this.slideCount = this.slides.length;

        console.log(`Carousel refreshed with ${this.slideCount} slides`);

        // Recreate indicators
        if (this.options.indicators) {
            const existingIndicators = this.carousel.querySelector('.carousel-indicators');
            if (existingIndicators) {
                existingIndicators.remove();
            }

            const existingSlideCounter = this.carousel.querySelector('.carousel-slide-counter');
            if (existingSlideCounter) {
                existingSlideCounter.remove();
            }

            this.createIndicators();
        }

        // Reset to first slide
        this.currentIndex = 0;
        this.goToSlide(0);

        // Restart autoplay if enabled
        if (this.options.autoplay) {
            this.resetAutoplay();
        }
    }
}


// Export for use in other scripts
if (typeof module !== 'undefined') {
    module.exports = { Carousel };
}
