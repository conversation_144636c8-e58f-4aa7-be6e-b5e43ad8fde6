<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yendor Cats Gallery V2 - High Performance Gallery</title>
    <!-- Build Version: __ASSET_VERSION__ (stamped at Docker build time via frontend/Dockerfile)
         Developers: You can verify in the browser console with window.__BUILD_VERSION__ -->
    <script>
      // Expose build version for diagnostics
      window.__BUILD_VERSION__ = "__ASSET_VERSION__";
    </script>

    <!-- Preload critical resources -->
    <!-- Cache-busting placeholders (__ASSET_VERSION__) are replaced at Docker build using git SHA -->
    <link rel="preload" href="css/gallery-v2.css?v=__ASSET_VERSION__" as="style">
    <link rel="preload" href="js/gallery-v2.js?v=__ASSET_VERSION__" as="script">

    <!-- Favicon -->
    <link rel="icon" href="images/favicon.ico" type="image/x-icon">

    <!-- CSS -->
    <link rel="stylesheet" href="css/variables.css?v=__ASSET_VERSION__">
    <link rel="stylesheet" href="css/base.css?v=__ASSET_VERSION__">
    <link rel="stylesheet" href="css/gallery-v2.css?v=__ASSET_VERSION__">

    <!-- Performance optimizations -->
    <link rel="dns-prefetch" href="//f002.backblazeb2.com">
    <link rel="dns-prefetch" href="//f004.backblazeb2.com">
    <link rel="preconnect" href="//api.yendorcats.com">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- Meta tags for SEO and social sharing -->
    <meta name="description" content="Yendor Cats Gallery - High-performance gallery showcasing Maine Coon cats with advanced breeding program and champion bloodlines.">
    <meta name="keywords" content="Maine Coon, cat gallery, breeding, champion bloodlines, Yendor Cats">
    <meta name="author" content="Yendor Cats">

    <!-- Open Graph meta tags -->
    <meta property="og:title" content="Yendor Cats Gallery V2">
    <meta property="og:description" content="High-performance gallery showcasing Maine Coon cats with advanced breeding program.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://yendorcats.com/gallery-v2.html">
    <meta property="og:image" content="https://yendorcats.com/images/logo_shield.webp">

    <!-- Twitter Card meta tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Yendor Cats Gallery V2">
    <meta name="twitter:description" content="High-performance gallery showcasing Maine Coon cats with advanced breeding program.">
    <meta name="twitter:image" content="https://yendorcats.com/images/logo_shield.webp">

    <!-- Performance monitoring -->
    <script>
        // Performance monitoring setup
        window.performanceMetrics = {
            startTime: performance.now(),
            navigationTiming: performance.getEntriesByType('navigation')[0],
            resourceTimings: [],
            customMetrics: {}
        };

        // Track resource loading
        window.addEventListener('load', function() {
            window.performanceMetrics.loadTime = performance.now() - window.performanceMetrics.startTime;
            window.performanceMetrics.resourceTimings = performance.getEntriesByType('resource');

            console.log('Page load performance:', {
                loadTime: window.performanceMetrics.loadTime,
                domContentLoaded: window.performanceMetrics.navigationTiming.domContentLoadedEventEnd - window.performanceMetrics.navigationTiming.navigationStart,
                resources: window.performanceMetrics.resourceTimings.length
            });
        });
    </script>
</head>
<body>
    <!-- Page header -->
    <header class="gallery-header">
        <div class="gallery-brand">
            <h1 class="gallery-title">Yendor Cats Gallery</h1>
            <p class="gallery-subtitle">High-Performance Cat Gallery System</p>
        </div>
        <div class="gallery-controls">
            <button class="btn-control" id="performance-toggle" title="Toggle Performance Monitor">
                <i class="fas fa-tachometer-alt"></i>
            </button>
            <button class="btn-control" id="category-selector" title="Select Category">
                <i class="fas fa-filter"></i>
            </button>
            <button class="btn-control" id="view-mode-toggle" title="Toggle View Mode">
                <i class="fas fa-th"></i>
            </button>
        </div>
    </header>

    <!-- Performance dashboard (hidden by default) -->
    <div id="performance-dashboard" class="performance-dashboard" style="display: none;">
        <div class="dashboard-header">
            <h3>Performance Monitor</h3>
            <button class="btn-close" id="close-dashboard">&times;</button>
        </div>
        <div class="dashboard-content">
            <div class="metric-group">
                <div class="metric-item">
                    <span class="metric-label">Cache Hit Rate</span>
                    <span class="metric-value" id="cache-hit-rate">0%</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">API Response Time</span>
                    <span class="metric-value" id="api-response-time">0ms</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Images Loaded</span>
                    <span class="metric-value" id="images-loaded">0</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Errors</span>
                    <span class="metric-value" id="error-count">0</span>
                </div>
            </div>
            <div class="performance-chart" id="performance-chart">
                <canvas id="performance-canvas" width="300" height="100"></canvas>
            </div>
        </div>
    </div>

    <!-- Category selector modal -->
    <div id="category-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Select Category</h3>
                <button class="btn-close" id="close-category-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="category-grid">
                    <button class="category-btn" data-category="studs">
                        <i class="fas fa-crown"></i>
                        <span>Studs</span>
                        <small>Breeding males</small>
                    </button>
                    <button class="category-btn" data-category="queens">
                        <i class="fas fa-heart"></i>
                        <span>Queens</span>
                        <small>Breeding females</small>
                    </button>
                    <button class="category-btn" data-category="kittens">
                        <i class="fas fa-baby"></i>
                        <span>Kittens</span>
                        <small>Available kittens</small>
                    </button>
                    <button class="category-btn" data-category="gallery">
                        <i class="fas fa-images"></i>
                        <span>Gallery</span>
                        <small>General photos</small>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main gallery container -->
    <main class="gallery-container">
        <!-- Gallery statistics -->
        <div class="gallery-stats">
            <div class="stat-item">
                <span class="stat-label">Images</span>
                <span class="stat-value" id="total-images">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Page</span>
                <span class="stat-value" id="current-page">1 of 1</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Source</span>
                <span class="stat-value" id="cache-source">loading</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Query Time</span>
                <span class="stat-value" id="query-time">0ms</span>
            </div>
        </div>

        <!-- Gallery grid (populated by JavaScript) -->
        <div class="gallery-grid-container">
            <div class="loading-placeholder" id="loading-placeholder">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                </div>
                <p>Loading high-performance gallery...</p>
                <small>Optimizing images and warming cache</small>
            </div>
            <!-- Gallery items will be inserted here -->
        </div>

        <!-- Gallery pagination -->
        <div class="gallery-pagination">
            <button class="btn-page" data-action="first" disabled>
                <i class="fas fa-angle-double-left"></i>
            </button>
            <button class="btn-page" data-action="prev" disabled>
                <i class="fas fa-angle-left"></i>
            </button>
            <span class="page-info">Page 1 of 1</span>
            <button class="btn-page" data-action="next" disabled>
                <i class="fas fa-angle-right"></i>
            </button>
            <button class="btn-page" data-action="last" disabled>
                <i class="fas fa-angle-double-right"></i>
            </button>
        </div>
    </main>

    <!-- Image lightbox modal -->
    <div id="image-lightbox" class="lightbox-modal" style="display: none;">
        <div class="lightbox-overlay" id="lightbox-overlay"></div>
        <div class="lightbox-content">
            <button class="lightbox-close" id="lightbox-close">&times;</button>
            <div class="lightbox-image-container">
                <img id="lightbox-image" src="" alt="" class="lightbox-image">
                <div class="lightbox-loading">
                    <div class="spinner"></div>
                </div>
            </div>
            <div class="lightbox-details">
                <h3 id="lightbox-title"></h3>
                <div class="lightbox-meta">
                    <span id="lightbox-cat-info"></span>
                    <span id="lightbox-date-info"></span>
                </div>
                <p id="lightbox-description"></p>
                <div class="lightbox-tags" id="lightbox-tags"></div>
            </div>
            <div class="lightbox-navigation">
                <button class="btn-nav" id="lightbox-prev">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="btn-nav" id="lightbox-next">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="gallery-footer">
        <div class="footer-content">
            <div class="footer-section">
                <h4>Yendor Cats</h4>
                <p>Professional Maine Coon breeding program with champion bloodlines.</p>
            </div>
            <div class="footer-section">
                <h4>Performance</h4>
                <p>Gallery V2 with 85-90% performance improvement over traditional S3 scanning.</p>
            </div>
            <div class="footer-section">
                <h4>Technology</h4>
                <p>Hybrid storage architecture with intelligent caching and lazy loading.</p>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2025 Yendor Cats. All rights reserved.</p>
            <p>Gallery V2 - High Performance Edition</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <!-- Cache-busted script: __ASSET_VERSION__ replaced at Docker build time with git SHA -->
    <script src="js/gallery-v2.js?v=__ASSET_VERSION__"></script>
    <script>
        // Gallery V2 Application
        class GalleryV2App {
            constructor() {
                this.gallery = null;
                this.currentCategory = 'studs';
                this.currentImageIndex = 0;
                this.images = [];
                this.performanceChart = null;
                this.init();
            }

            init() {
                this.setupGallery();
                this.setupEventListeners();
                this.setupPerformanceMonitoring();
                this.loadInitialGallery();
            }

            setupGallery() {
                // Initialize Gallery V2 with performance optimizations
                this.gallery = new GalleryV2({
                    baseUrl: '/api/v2/gallery',
                    fallbackUrl: '/api/CatGallery',
                    cacheExpiry: 5 * 60 * 1000, // 5 minutes
                    localStorageExpiry: 30 * 60 * 1000, // 30 minutes
                    lazyLoadThreshold: 200,
                    preloadCount: 3,
                    enablePerformanceMonitoring: true
                });

                // Set up gallery event listeners
                this.gallery.on('gallery:loaded', (data) => {
                    this.handleGalleryLoaded(data);
                });

                this.gallery.on('image:view', (data) => {
                    this.showLightbox(data.item);
                });

                this.gallery.on('image:details', (data) => {
                    this.showImageDetails(data.item);
                });

                this.gallery.on('performance:metrics', (metrics) => {
                    this.updatePerformanceMetrics(metrics);
                });

                this.gallery.on('gallery:online', () => {
                    this.handleOnlineStatus(true);
                });

                this.gallery.on('gallery:offline', () => {
                    this.handleOnlineStatus(false);
                });
            }

            setupEventListeners() {
                // Performance dashboard toggle
                document.getElementById('performance-toggle').addEventListener('click', () => {
                    this.togglePerformanceDashboard();
                });

                document.getElementById('close-dashboard').addEventListener('click', () => {
                    this.togglePerformanceDashboard();
                });

                // Category selector
                document.getElementById('category-selector').addEventListener('click', () => {
                    this.showCategoryModal();
                });

                document.getElementById('close-category-modal').addEventListener('click', () => {
                    this.hideCategoryModal();
                });

                // Category buttons
                document.querySelectorAll('.category-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const category = e.currentTarget.dataset.category;
                        this.loadCategory(category);
                        this.hideCategoryModal();
                    });
                });

                // View mode toggle
                document.getElementById('view-mode-toggle').addEventListener('click', () => {
                    this.toggleViewMode();
                });

                // Lightbox controls
                document.getElementById('lightbox-close').addEventListener('click', () => {
                    this.hideLightbox();
                });

                document.getElementById('lightbox-overlay').addEventListener('click', () => {
                    this.hideLightbox();
                });

                document.getElementById('lightbox-prev').addEventListener('click', () => {
                    this.showPreviousImage();
                });

                document.getElementById('lightbox-next').addEventListener('click', () => {
                    this.showNextImage();
                });

                // Keyboard navigation
                document.addEventListener('keydown', (e) => {
                    this.handleKeyboard(e);
                });

                // Window resize handler
                window.addEventListener('resize', () => {
                    this.handleResize();
                });
            }

            setupPerformanceMonitoring() {
                // Set up performance chart
                const canvas = document.getElementById('performance-canvas');
                const ctx = canvas.getContext('2d');

                this.performanceChart = {
                    canvas: canvas,
                    ctx: ctx,
                    data: [],
                    maxPoints: 20
                };

                // Update performance metrics every 5 seconds
                setInterval(() => {
                    this.updatePerformanceChart();
                }, 5000);
            }

            async loadInitialGallery() {
                try {
                    this.showLoadingState(true);
                    await this.gallery.loadCategory(this.currentCategory);
                    this.showLoadingState(false);
                } catch (error) {
                    console.error('Error loading initial gallery:', error);
                    this.showLoadingState(false);
                }
            }

            async loadCategory(category) {
                try {
                    this.currentCategory = category;
                    this.showLoadingState(true);

                    const data = await this.gallery.loadCategory(category);
                    this.images = data.items || [];
                    this.currentImageIndex = 0;

                    this.showLoadingState(false);
                    this.updatePageTitle(category);

                } catch (error) {
                    console.error('Error loading category:', error);
                    this.showLoadingState(false);
                }
            }

            handleGalleryLoaded(data) {
                this.images = data.data.items || [];
                this.hideLoadingState();

                // Update statistics
                this.updateStats(data.data);

                // Update performance metrics
                this.updatePerformanceMetrics(this.gallery.performanceMetrics);

                console.log(`Gallery loaded: ${this.images.length} images`);
            }

            updateStats(data) {
                document.getElementById('total-images').textContent = data.totalCount || 0;
                document.getElementById('current-page').textContent = `${data.page || 1} of ${data.totalPages || 1}`;
                document.getElementById('cache-source').textContent = data.cacheSource || 'unknown';
                document.getElementById('query-time').textContent = `${(data.queryTime || 0).toFixed(0)}ms`;
            }

            updatePerformanceMetrics(metrics) {
                const cacheHitRate = metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses) * 100 || 0;
                const avgApiTime = metrics.apiCalls > 0 ? metrics.totalLoadTime / metrics.apiCalls : 0;

                document.getElementById('cache-hit-rate').textContent = `${cacheHitRate.toFixed(1)}%`;
                document.getElementById('api-response-time').textContent = `${avgApiTime.toFixed(0)}ms`;
                document.getElementById('images-loaded').textContent = metrics.imageLoadTimes.length;
                document.getElementById('error-count').textContent = metrics.errors;

                // Update performance chart
                this.performanceChart.data.push({
                    timestamp: Date.now(),
                    cacheHitRate: cacheHitRate,
                    apiTime: avgApiTime,
                    errors: metrics.errors
                });

                // Keep only last 20 data points
                if (this.performanceChart.data.length > this.performanceChart.maxPoints) {
                    this.performanceChart.data.shift();
                }
            }

            updatePerformanceChart() {
                const chart = this.performanceChart;
                const ctx = chart.ctx;
                const canvas = chart.canvas;
                const data = chart.data;

                if (data.length === 0) return;

                // Clear canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Set up drawing context
                ctx.strokeStyle = '#3498db';
                ctx.lineWidth = 2;
                ctx.fillStyle = 'rgba(52, 152, 219, 0.1)';

                // Draw cache hit rate line
                ctx.beginPath();
                data.forEach((point, index) => {
                    const x = (index / (data.length - 1)) * canvas.width;
                    const y = canvas.height - (point.cacheHitRate / 100) * canvas.height;

                    if (index === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                });
                ctx.stroke();

                // Draw API response time (scaled)
                ctx.strokeStyle = '#e74c3c';
                ctx.beginPath();
                data.forEach((point, index) => {
                    const x = (index / (data.length - 1)) * canvas.width;
                    const y = canvas.height - Math.min(point.apiTime / 1000, 1) * canvas.height;

                    if (index === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                });
                ctx.stroke();
            }

            showLightbox(item) {
                const lightbox = document.getElementById('image-lightbox');
                const lightboxImage = document.getElementById('lightbox-image');
                const lightboxLoading = document.querySelector('.lightbox-loading');

                // Find current image index
                this.currentImageIndex = this.images.findIndex(img => img.id === item.id);

                // Show lightbox
                lightbox.style.display = 'block';
                document.body.style.overflow = 'hidden';

                // Update lightbox content
                this.updateLightboxContent(item);

                // Show loading state
                lightboxLoading.style.display = 'block';
                lightboxImage.style.opacity = '0';

                // Load image
                lightboxImage.onload = () => {
                    lightboxLoading.style.display = 'none';
                    lightboxImage.style.opacity = '1';
                };

                lightboxImage.src = item.imageUrl;
            }

            updateLightboxContent(item) {
                document.getElementById('lightbox-title').textContent = item.title || item.catName || 'Unnamed Cat';
                document.getElementById('lightbox-cat-info').textContent =
                    `${item.breed || 'Maine Coon'}${item.bloodline ? ` • ${item.bloodline}` : ''}${item.gender ? ` • ${item.gender}` : ''}`;
                document.getElementById('lightbox-date-info').textContent =
                    item.dateTaken ? new Date(item.dateTaken).toLocaleDateString() : '';
                document.getElementById('lightbox-description').textContent = item.description || '';

                // Update tags
                const tagsContainer = document.getElementById('lightbox-tags');
                if (item.tags) {
                    const tags = typeof item.tags === 'string' ? item.tags.split(',') : item.tags;
                    tagsContainer.innerHTML = tags.map(tag =>
                        `<span class="tag">${tag.trim()}</span>`
                    ).join('');
                } else {
                    tagsContainer.innerHTML = '';
                }
            }

            hideLightbox() {
                document.getElementById('image-lightbox').style.display = 'none';
                document.body.style.overflow = 'auto';
            }

            showPreviousImage() {
                if (this.currentImageIndex > 0) {
                    this.currentImageIndex--;
                    const item = this.images[this.currentImageIndex];
                    this.updateLightboxContent(item);
                    document.getElementById('lightbox-image').src = item.imageUrl;
                }
            }

            showNextImage() {
                if (this.currentImageIndex < this.images.length - 1) {
                    this.currentImageIndex++;
                    const item = this.images[this.currentImageIndex];
                    this.updateLightboxContent(item);
                    document.getElementById('lightbox-image').src = item.imageUrl;
                }
            }

            togglePerformanceDashboard() {
                const dashboard = document.getElementById('performance-dashboard');
                dashboard.style.display = dashboard.style.display === 'none' ? 'block' : 'none';
            }

            showCategoryModal() {
                document.getElementById('category-modal').style.display = 'block';
            }

            hideCategoryModal() {
                document.getElementById('category-modal').style.display = 'none';
            }

            toggleViewMode() {
                const grid = document.querySelector('.gallery-grid-v2');
                if (grid) {
                    grid.classList.toggle('compact-view');
                }
            }

            showLoadingState(show) {
                const placeholder = document.getElementById('loading-placeholder');
                if (show) {
                    placeholder.style.display = 'block';
                } else {
                    placeholder.style.display = 'none';
                }
            }

            hideLoadingState() {
                this.showLoadingState(false);
            }

            updatePageTitle(category) {
                const categoryNames = {
                    studs: 'Studs',
                    queens: 'Queens',
                    kittens: 'Kittens',
                    gallery: 'Gallery'
                };

                document.title = `${categoryNames[category] || category} - Yendor Cats Gallery V2`;
            }

            handleKeyboard(e) {
                // Handle keyboard navigation
                if (document.getElementById('image-lightbox').style.display === 'block') {
                    switch (e.key) {
                        case 'Escape':
                            this.hideLightbox();
                            break;
                        case 'ArrowLeft':
                            this.showPreviousImage();
                            break;
                        case 'ArrowRight':
                            this.showNextImage();
                            break;
                    }
                }
            }

            handleResize() {
                // Handle window resize
                if (this.performanceChart) {
                    this.updatePerformanceChart();
                }
            }

            handleOnlineStatus(isOnline) {
                const statusIndicator = document.querySelector('.status-indicator');
                if (statusIndicator) {
                    statusIndicator.textContent = isOnline ? 'Online' : 'Offline';
                    statusIndicator.className = `status-indicator ${isOnline ? 'online' : 'offline'}`;
                }
            }

            showImageDetails(item) {
                // Show detailed image information
                console.log('Image details:', item);
                // This could open a detailed modal or navigate to a details page
            }
        }

        // Initialize the application when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            window.galleryApp = new GalleryV2App();

            // Track page load performance
            window.addEventListener('load', () => {
                const loadTime = performance.now() - window.performanceMetrics.startTime;
                console.log(`Gallery V2 page loaded in ${loadTime.toFixed(2)}ms`);

                // Send performance metrics to analytics (if configured)
                if (window.gtag) {
                    gtag('event', 'page_load_time', {
                        custom_parameter: Math.round(loadTime)
                    });
                }
            });
        });
    </script>

    <!-- Additional styles for this page -->
    <style>
        /* Gallery V2 specific styles */
        .gallery-brand {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .gallery-controls {
            display: flex;
            gap: var(--spacing-sm);
        }

        .btn-control {
            background: var(--secondary-color);
            color: var(--text-light);
            border: none;
            border-radius: var(--border-radius-md);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-fast);
            font-size: 16px;
        }

        .btn-control:hover {
            background: var(--secondary-dark);
            transform: translateY(-1px);
        }

        /* Performance Dashboard */
        .performance-dashboard {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-color);
            border: 2px solid var(--bg-medium);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            z-index: var(--z-popover);
            width: 350px;
            max-width: 90vw;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--bg-medium);
        }

        .dashboard-header h3 {
            margin: 0;
            color: var(--text-primary);
        }

        .btn-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--text-secondary);
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-close:hover {
            color: var(--text-primary);
        }

        .dashboard-content {
            padding: var(--spacing-md);
        }

        .metric-group {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .metric-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: var(--spacing-sm);
            background: var(--bg-light);
            border-radius: var(--border-radius-sm);
        }

        .metric-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }

        .metric-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .performance-chart {
            background: var(--bg-light);
            border-radius: var(--border-radius-sm);
            padding: var(--spacing-sm);
        }

        /* Category Modal */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: var(--z-modal);
        }

        .modal-content {
            background: var(--bg-color);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-xl);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--bg-medium);
        }

        .modal-header h3 {
            margin: 0;
            color: var(--text-primary);
        }

        .modal-body {
            padding: var(--spacing-lg);
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }

        .category-btn {
            background: var(--bg-light);
            border: 2px solid var(--bg-medium);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-sm);
            cursor: pointer;
            transition: var(--transition-fast);
            text-align: center;
        }

        .category-btn:hover {
            background: var(--secondary-color);
            color: var(--text-light);
            border-color: var(--secondary-color);
            transform: translateY(-2px);
        }

        .category-btn i {
            font-size: 2rem;
        }

        .category-btn span {
            font-weight: 600;
            font-size: 1.1rem;
        }

        .category-btn small {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Lightbox Modal */
        .lightbox-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: var(--z-modal);
        }

        .lightbox-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
        }

        .lightbox-content {
            position: relative;
            display: flex;
            width: 100%;
            height: 100%;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-xl);
        }

        .lightbox-close {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 24px;
            cursor: pointer;
            z-index: 10;
        }

        .lightbox-image-container {
            position: relative;
            max-width: 80%;
            max-height: 80%;
        }

        .lightbox-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .lightbox-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* Footer */
        .gallery-footer {
            background: var(--bg-light);
            padding: var(--spacing-xl) 0;
            margin-top: var(--spacing-xxl);
            border-top: 2px solid var(--bg-medium);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            padding: 0 var(--spacing-lg);
        }

        .footer-section h4 {
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .footer-section p {
            color: var(--text-secondary);
            line-height: 1.5;
        }

        .footer-bottom {
            text-align: center;
            padding-top: var(--spacing-lg);
            border-top: 1px solid var(--bg-medium);
            margin-top: var(--spacing-lg);
            color: var(--text-muted);
        }

        /* Loading States */
        .loading-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-xxl);
            text-align: center;
            min-height: 400px;
        }

        .loading-spinner {
            margin-bottom: var(--spacing-lg);
        }

        .loading-placeholder p {
            font-size: 1.2rem;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .loading-placeholder small {
            color: var(--text-secondary);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .category-grid {
                grid-template-columns: 1fr;
            }

            .lightbox-content {
                padding: var(--spacing-md);
            }

            .lightbox-image-container {
                max-width: 95%;
                max-height: 95%;
            }
        }
    </style>
</body>
</html>
