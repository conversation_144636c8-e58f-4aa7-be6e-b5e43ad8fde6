using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using System.Net;

namespace YendorCats.API.Middleware
{
    /// <summary>
    /// Enhanced rate limiting middleware with global and endpoint-specific limits
    /// </summary>
    public class EnhancedRateLimitingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IMemoryCache _cache;
        private readonly ILogger<EnhancedRateLimitingMiddleware> _logger;
        private readonly RateLimitingOptions _options;

        public EnhancedRateLimitingMiddleware(
            RequestDelegate next, 
            IMemoryCache cache, 
            ILogger<EnhancedRateLimitingMiddleware> logger,
            IOptions<RateLimitingOptions> options)
        {
            _next = next;
            _cache = cache;
            _logger = logger;
            _options = options.Value;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var clientIp = GetClientIpAddress(context);
            var endpoint = context.Request.Path.Value?.ToLowerInvariant();

            // Check global rate limit first
            if (_options.EnableGlobalRateLimiting)
            {
                var globalLimitResult = await CheckRateLimitAsync(clientIp, "global", _options.GlobalLimit, _options.GlobalWindow);
                if (!globalLimitResult.IsAllowed)
                {
                    await ReturnRateLimitResponse(context, globalLimitResult);
                    return;
                }
            }

            // Check endpoint-specific rate limits
            if (endpoint != null && _options.EndpointLimits.ContainsKey(endpoint))
            {
                var (requests, window) = _options.EndpointLimits[endpoint];
                var endpointLimitResult = await CheckRateLimitAsync(clientIp, endpoint, requests, window);
                if (!endpointLimitResult.IsAllowed)
                {
                    await ReturnRateLimitResponse(context, endpointLimitResult);
                    return;
                }
            }

            // Check IP-based rate limiting
            if (_options.EnableIpRateLimiting)
            {
                var ipLimitResult = await CheckRateLimitAsync(clientIp, $"ip_{clientIp}", _options.IpLimit, _options.IpWindow);
                if (!ipLimitResult.IsAllowed)
                {
                    await ReturnRateLimitResponse(context, ipLimitResult);
                    return;
                }
            }

            await _next(context);
        }

        private async Task<RateLimitResult> CheckRateLimitAsync(string key, string limitKey, int maxRequests, TimeSpan window)
        {
            var cacheKey = $"rate_limit_{key}_{limitKey}";
            var lockKey = $"rate_limit_lock_{key}_{limitKey}";

            // Simple locking mechanism to prevent race conditions
            var lockObj = _cache.GetOrCreate(lockKey, entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(1);
                return new object();
            });

            lock (lockObj)
            {
                var requestCount = _cache.Get<int>(cacheKey);
                
                if (requestCount >= maxRequests)
                {
                    var expiryTime = _cache.Get<DateTimeOffset?>(cacheKey + "_expiry");
                    var retryAfter = expiryTime.HasValue ? Math.Max(0, (int)(expiryTime.Value - DateTimeOffset.UtcNow).TotalSeconds) : (int)window.TotalSeconds;
                    
                    return new RateLimitResult
                    {
                        IsAllowed = false,
                        RetryAfterSeconds = retryAfter,
                        RequestsRemaining = 0,
                        ResetTime = expiryTime
                    };
                }

                _cache.Set(cacheKey, requestCount + 1, window);
                _cache.Set(cacheKey + "_expiry", DateTimeOffset.UtcNow.Add(window), window);

                return new RateLimitResult
                {
                    IsAllowed = true,
                    RetryAfterSeconds = (int)window.TotalSeconds,
                    RequestsRemaining = maxRequests - requestCount - 1,
                    ResetTime = DateTimeOffset.UtcNow.Add(window)
                };
            }
        }

        private async Task ReturnRateLimitResponse(HttpContext context, RateLimitResult result)
        {
            context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
            context.Response.Headers.Add("Retry-After", result.RetryAfterSeconds.ToString());
            context.Response.Headers.Add("X-RateLimit-Remaining", result.RequestsRemaining.ToString());
            context.Response.Headers.Add("X-RateLimit-Reset", result.ResetTime?.ToUnixTimeSeconds().ToString() ?? "0");

            var response = new
            {
                error = "Rate limit exceeded",
                message = "Too many requests. Please try again later.",
                retryAfter = result.RetryAfterSeconds
            };

            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
        }

        private string GetClientIpAddress(HttpContext context)
        {
            // Check for forwarded IP first (for load balancers/proxies)
            var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                return forwardedFor.Split(',')[0].Trim();
            }

            var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp))
            {
                return realIp;
            }

            return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
        }
    }

    public class RateLimitResult
    {
        public bool IsAllowed { get; set; }
        public int RetryAfterSeconds { get; set; }
        public int RequestsRemaining { get; set; }
        public DateTimeOffset? ResetTime { get; set; }
    }

    public class RateLimitingOptions
    {
        public bool EnableGlobalRateLimiting { get; set; } = true;
        public int GlobalLimit { get; set; } = 1000;
        public TimeSpan GlobalWindow { get; set; } = TimeSpan.FromMinutes(10);

        public bool EnableIpRateLimiting { get; set; } = true;
        public int IpLimit { get; set; } = 100;
        public TimeSpan IpWindow { get; set; } = TimeSpan.FromMinutes(10);

        public Dictionary<string, (int requests, TimeSpan window)> EndpointLimits { get; set; } = new()
        {
            { "/api/auth/login", (5, TimeSpan.FromMinutes(15)) },
            { "/api/adminauth/login", (3, TimeSpan.FromMinutes(15)) },
            { "/api/auth/register", (3, TimeSpan.FromHours(1)) },
            { "/api/photoupload/upload", (10, TimeSpan.FromMinutes(5)) },
            { "/api/sync/manual", (2, TimeSpan.FromMinutes(1)) }
        };
    }
}