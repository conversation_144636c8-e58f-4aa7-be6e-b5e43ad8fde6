using System.ComponentModel.DataAnnotations;

namespace YendorCats.API.Models
{
    /// <summary>
    /// Model representing a newsletter subscription
    /// </summary>
    public class NewsletterSubscription
    {
        /// <summary>
        /// Unique identifier for the subscription
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Subscriber's name
        /// </summary>
        [Required]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Subscriber's email address
        /// </summary>
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Interest category selected by subscriber
        /// </summary>
        public string? Interests { get; set; }

        /// <summary>
        /// When the subscription was created
        /// </summary>
        public DateTime SubscribedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// IP address of the subscriber
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// Status of the subscription (new, contacted, etc.)
        /// </summary>
        public string Status { get; set; } = "new";

        /// <summary>
        /// Admin notes about this subscription
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// When the subscription was last updated
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Who last updated this subscription (admin username)
        /// </summary>
        public string? LastUpdatedBy { get; set; }
    }

    /// <summary>
    /// Request model for newsletter subscription
    /// </summary>
    public class NewsletterSubscriptionRequest
    {
        /// <summary>
        /// Subscriber's name
        /// </summary>
        [Required]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Subscriber's email address
        /// </summary>
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Interest category selected by subscriber
        /// </summary>
        public string? Interests { get; set; }
    }

    /// <summary>
    /// Request model for updating newsletter subscription status
    /// </summary>
    public class UpdateNewsletterStatusRequest
    {
        /// <summary>
        /// New status for the subscription
        /// </summary>
        [Required]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Admin notes
        /// </summary>
        public string? Notes { get; set; }
    }
}
