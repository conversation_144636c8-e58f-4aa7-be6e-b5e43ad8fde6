using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Interface for newsletter subscription service
    /// </summary>
    public interface INewsletterService
    {
        /// <summary>
        /// Add a new newsletter subscription
        /// </summary>
        /// <param name="subscription">Newsletter subscription to add</param>
        /// <returns>True if successful</returns>
        Task<bool> AddSubscriptionAsync(NewsletterSubscription subscription);

        /// <summary>
        /// Get all newsletter subscriptions
        /// </summary>
        /// <returns>List of newsletter subscriptions</returns>
        Task<List<NewsletterSubscription>> GetAllSubscriptionsAsync();

        /// <summary>
        /// Get a specific newsletter subscription by ID
        /// </summary>
        /// <param name="id">Subscription ID</param>
        /// <returns>Newsletter subscription or null if not found</returns>
        Task<NewsletterSubscription?> GetSubscriptionByIdAsync(string id);

        /// <summary>
        /// Update a newsletter subscription status
        /// </summary>
        /// <param name="id">Subscription ID</param>
        /// <param name="status">New status</param>
        /// <param name="notes">Admin notes</param>
        /// <param name="updatedBy">Username of who updated it</param>
        /// <returns>True if successful</returns>
        Task<bool> UpdateSubscriptionStatusAsync(string id, string status, string? notes, string updatedBy);

        /// <summary>
        /// Check if an email is already subscribed
        /// </summary>
        /// <param name="email">Email address to check</param>
        /// <returns>True if email is already subscribed</returns>
        Task<bool> IsEmailSubscribedAsync(string email);

        /// <summary>
        /// Get subscriptions by status
        /// </summary>
        /// <param name="status">Status to filter by</param>
        /// <returns>List of matching subscriptions</returns>
        Task<List<NewsletterSubscription>> GetSubscriptionsByStatusAsync(string status);
    }
}
