using System.Text.Json;
using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Photo index service implementation that works without S3 credentials for public access
    /// </summary>
    public class PhotoIndexService : IPhotoIndexService
    {
        private readonly ILogger<PhotoIndexService> _logger;
        private readonly IConfiguration _configuration;
        private readonly IS3StorageService? _s3StorageService;
        private readonly string _indexFilePath;
        private PhotoIndex? _cachedIndex;
        private DateTime _lastCacheUpdate = DateTime.MinValue;
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5);

        public PhotoIndexService(
            ILogger<PhotoIndexService> logger,
            IConfiguration configuration,
            IS3StorageService? s3StorageService = null)
        {
            _logger = logger;
            _configuration = configuration;
            _s3StorageService = s3StorageService;
            _indexFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "PhotoIndex.json");
        }

        public async Task<List<CatGalleryImage>> GetCategoryPhotosAsync(string category)
        {
            try
            {
                var index = await GetPhotoIndexAsync();
                var photos = new List<CatGalleryImage>();

                if (!index.Categories.ContainsKey(category.ToLower()))
                {
                    _logger.LogWarning("Category not found in index: {Category}", category);
                    return photos;
                }

                var categoryData = index.Categories[category.ToLower()];
                var publicUrlTemplate = _configuration["AWS:S3:PublicUrl"] ?? "https://f004.backblazeb2.com/file/yendor/{key}";

                foreach (var catName in categoryData.Keys)
                {
                    foreach (var fileName in categoryData[catName])
                    {
                        var s3Key = $"{category}/{catName}/{fileName}";
                        var imageUrl = publicUrlTemplate.Replace("{key}", s3Key);
                        
                        _logger.LogInformation("Generated URL for {S3Key}: {ImageUrl} (template: {Template})", s3Key, imageUrl, publicUrlTemplate);
                        
                        var photo = CreateCatGalleryImage(s3Key, imageUrl, category, catName, index);
                        photos.Add(photo);
                    }
                }

                _logger.LogInformation("Retrieved {Count} photos for category {Category} from index", photos.Count, category);
                return photos.OrderBy(p => p.CatName).ThenBy(p => p.DateTaken).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving category photos from index: {Category}", category);
                return new List<CatGalleryImage>();
            }
        }

        public async Task<List<CatGalleryImage>> GetCatPhotosAsync(string catName)
        {
            try
            {
                var index = await GetPhotoIndexAsync();
                var photos = new List<CatGalleryImage>();
                var publicUrlTemplate = _configuration["AWS:S3:PublicUrl"] ?? "https://f004.backblazeb2.com/file/yendor/{key}";

                foreach (var category in index.Categories.Keys)
                {
                    var categoryData = index.Categories[category];
                    
                    // Find cat in this category (case-insensitive)
                    var matchingCat = categoryData.Keys.FirstOrDefault(c => 
                        c.Equals(catName, StringComparison.OrdinalIgnoreCase));

                    if (matchingCat != null)
                    {
                        foreach (var fileName in categoryData[matchingCat])
                        {
                            var s3Key = $"{category}/{matchingCat}/{fileName}";
                            var imageUrl = publicUrlTemplate.Replace("{key}", s3Key);
                            
                            var photo = CreateCatGalleryImage(s3Key, imageUrl, category, matchingCat, index);
                            photos.Add(photo);
                        }
                    }
                }

                _logger.LogInformation("Retrieved {Count} photos for cat {CatName} from index", photos.Count, catName);
                return photos.OrderBy(p => p.DateTaken).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cat photos from index: {CatName}", catName);
                return new List<CatGalleryImage>();
            }
        }

        public async Task<List<CatGalleryImage>> GetFeaturedPhotosAsync(int count)
        {
            try
            {
                var index = await GetPhotoIndexAsync();
                var allPhotos = new List<CatGalleryImage>();
                var publicUrlTemplate = _configuration["AWS:S3:PublicUrl"] ?? "https://f004.backblazeb2.com/file/yendor/{key}";

                foreach (var category in index.Categories.Keys)
                {
                    var categoryData = index.Categories[category];
                    
                    foreach (var catName in categoryData.Keys)
                    {
                        // Take only first photo from each cat for featured
                        var fileName = categoryData[catName].FirstOrDefault();
                        if (!string.IsNullOrEmpty(fileName))
                        {
                            var s3Key = $"{category}/{catName}/{fileName}";
                            var imageUrl = publicUrlTemplate.Replace("{key}", s3Key);
                            
                            var photo = CreateCatGalleryImage(s3Key, imageUrl, category, catName, index);
                            allPhotos.Add(photo);
                        }
                    }
                }

                var random = new Random();
                var featuredPhotos = allPhotos
                    .OrderBy(x => random.Next())
                    .Take(count)
                    .ToList();

                _logger.LogInformation("Retrieved {Count} featured photos from index", featuredPhotos.Count);
                return featuredPhotos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving featured photos from index");
                return new List<CatGalleryImage>();
            }
        }

        public async Task<bool> RefreshIndexAsync()
        {
            try
            {
                if (_s3StorageService == null)
                {
                    _logger.LogWarning("Cannot refresh index: S3 service not available (no credentials)");
                    return false;
                }

                _logger.LogInformation("Starting photo index refresh from S3");
                
                var newIndex = new PhotoIndex
                {
                    LastUpdated = DateTime.UtcNow,
                    Categories = new Dictionary<string, Dictionary<string, List<string>>>(),
                    Metadata = new Dictionary<string, PhotoMetadata>()
                };

                var categories = new[] { "studs", "queens", "kittens" };

                foreach (var category in categories)
                {
                    try
                    {
                        var files = await _s3StorageService.ListFilesAsync($"{category}/");
                        var categoryData = new Dictionary<string, List<string>>();

                        foreach (var file in files)
                        {
                            if (IsImageFile(file.Key))
                            {
                                // Remove the key prefix to get the relative path
                                var relativePath = _s3StorageService?.RemoveKeyPrefix(file.Key) ?? file.Key;
                                var pathParts = relativePath.Split('/');
                                string catName;
                                string fileName;

                                if (pathParts.Length == 2)
                                {
                                    // Direct in category: queens/IMG_4406.jpg
                                    catName = "General";
                                    fileName = pathParts[1];
                                }
                                else if (pathParts.Length == 3)
                                {
                                    // In cat subfolder: queens/Cat1/TESTING-IMG_4275.jpg
                                    catName = pathParts[1];
                                    fileName = pathParts[2];
                                }
                                else
                                {
                                    _logger.LogWarning("Unexpected S3 object path format: {Key} (relative: {RelativePath})", file.Key, relativePath);
                                    continue;
                                }

                                // Skip .bzEmpty files
                                if (fileName == ".bzEmpty")
                                    continue;

                                if (!categoryData.ContainsKey(catName))
                                {
                                    categoryData[catName] = new List<string>();
                                }
                                categoryData[catName].Add(fileName);

                                // Create metadata for this image
                                newIndex.Metadata[file.Key] = new PhotoMetadata
                                {
                                    CatName = catName,
                                    Age = "0", // Default
                                    Description = $"Beautiful {catName} from our {category} collection",
                                    Breed = "Maine Coon",
                                    Gender = category == "studs" ? "Male" : category == "queens" ? "Female" : "Unknown",
                                    Color = "Various",
                                    DateTaken = file.LastModified.ToString("yyyy-MM-dd"),
                                    Personality = "Loving and gentle",
                                    Bloodline = "Champion lineage"
                                };
                            }
                        }

                        if (categoryData.Any())
                        {
                            newIndex.Categories[category] = categoryData;
                            _logger.LogInformation("Found {Count} images in category {Category}", 
                                categoryData.Values.SelectMany(f => f).Count(), category);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing category {Category} during index refresh", category);
                    }
                }

                // Save the new index
                await SavePhotoIndexAsync(newIndex);
                _cachedIndex = newIndex;
                _lastCacheUpdate = DateTime.UtcNow;

                var totalPhotos = newIndex.Categories.Values.SelectMany(c => c.Values).SelectMany(f => f).Count();
                _logger.LogInformation("Photo index refresh completed. Found {CategoryCount} categories with {PhotoCount} total photos", 
                    newIndex.Categories.Count, totalPhotos);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing photo index");
                return false;
            }
        }

        public async Task<bool> AddPhotoToIndexAsync(string category, string catName, string fileName, Dictionary<string, string>? metadata = null)
        {
            try
            {
                var index = await GetPhotoIndexAsync();
                
                if (!index.Categories.ContainsKey(category))
                {
                    index.Categories[category] = new Dictionary<string, List<string>>();
                }

                if (!index.Categories[category].ContainsKey(catName))
                {
                    index.Categories[category][catName] = new List<string>();
                }

                if (!index.Categories[category][catName].Contains(fileName))
                {
                    index.Categories[category][catName].Add(fileName);
                    
                    // Add metadata if provided
                    if (metadata != null)
                    {
                        var s3Key = $"{category}/{catName}/{fileName}";
                        index.Metadata[s3Key] = new PhotoMetadata
                        {
                            CatName = metadata.GetValueOrDefault("cat-name", catName),
                            Age = metadata.GetValueOrDefault("age", "0"),
                            Description = metadata.GetValueOrDefault("description", ""),
                            Breed = metadata.GetValueOrDefault("breed", "Maine Coon"),
                            Gender = metadata.GetValueOrDefault("gender", ""),
                            Color = metadata.GetValueOrDefault("color", ""),
                            DateTaken = metadata.GetValueOrDefault("date-taken", ""),
                            Personality = metadata.GetValueOrDefault("personality", ""),
                            Bloodline = metadata.GetValueOrDefault("bloodline", "")
                        };
                    }

                    index.LastUpdated = DateTime.UtcNow;
                    await SavePhotoIndexAsync(index);
                    _cachedIndex = index;
                    
                    _logger.LogInformation("Added photo to index: {Category}/{CatName}/{FileName}", category, catName, fileName);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding photo to index: {Category}/{CatName}/{FileName}", category, catName, fileName);
                return false;
            }
        }

        public async Task<bool> RemovePhotoFromIndexAsync(string category, string catName, string fileName)
        {
            try
            {
                var index = await GetPhotoIndexAsync();
                
                if (index.Categories.ContainsKey(category) && 
                    index.Categories[category].ContainsKey(catName))
                {
                    var removed = index.Categories[category][catName].Remove(fileName);
                    
                    if (removed)
                    {
                        // Remove metadata
                        var s3Key = $"{category}/{catName}/{fileName}";
                        index.Metadata.Remove(s3Key);
                        
                        // Clean up empty entries
                        if (!index.Categories[category][catName].Any())
                        {
                            index.Categories[category].Remove(catName);
                        }
                        
                        if (!index.Categories[category].Any())
                        {
                            index.Categories.Remove(category);
                        }

                        index.LastUpdated = DateTime.UtcNow;
                        await SavePhotoIndexAsync(index);
                        _cachedIndex = index;
                        
                        _logger.LogInformation("Removed photo from index: {Category}/{CatName}/{FileName}", category, catName, fileName);
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing photo from index: {Category}/{CatName}/{FileName}", category, catName, fileName);
                return false;
            }
        }

        private async Task<PhotoIndex> GetPhotoIndexAsync()
        {
            // Return cached index if still valid
            if (_cachedIndex != null && DateTime.UtcNow - _lastCacheUpdate < _cacheExpiry)
            {
                return _cachedIndex;
            }

            try
            {
                if (File.Exists(_indexFilePath))
                {
                    var json = await File.ReadAllTextAsync(_indexFilePath);
                    var index = JsonSerializer.Deserialize<PhotoIndex>(json, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (index != null)
                    {
                        _cachedIndex = index;
                        _lastCacheUpdate = DateTime.UtcNow;
                        return index;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading photo index from file");
            }

            // Return empty index if file doesn't exist or can't be loaded
            var emptyIndex = new PhotoIndex
            {
                LastUpdated = DateTime.UtcNow,
                Categories = new Dictionary<string, Dictionary<string, List<string>>>(),
                Metadata = new Dictionary<string, PhotoMetadata>()
            };

            _cachedIndex = emptyIndex;
            _lastCacheUpdate = DateTime.UtcNow;
            return emptyIndex;
        }

        private async Task SavePhotoIndexAsync(PhotoIndex index)
        {
            try
            {
                var directory = Path.GetDirectoryName(_indexFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = JsonSerializer.Serialize(index, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                await File.WriteAllTextAsync(_indexFilePath, json);
                _logger.LogDebug("Saved photo index to {FilePath}", _indexFilePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving photo index to file");
                throw;
            }
        }

        private CatGalleryImage CreateCatGalleryImage(string s3Key, string imageUrl, string category, string catName, PhotoIndex index)
        {
            var metadata = index.Metadata.GetValueOrDefault(s3Key);
            
            return new CatGalleryImage
            {
                Id = 0,
                CatName = metadata?.CatName ?? ExtractCatNameFromPath(s3Key),
                StorageKey = s3Key,
                StorageBucketName = "yendor",
                StorageProvider = "B2",
                Category = category,
                DateTaken = ParseDateTaken(metadata?.DateTaken),
                OrderNumber = 1,
                Age = ParseAge(metadata?.Age),
                Description = metadata?.Description ?? "",
                Color = metadata?.Color ?? "",
                Gender = metadata?.Gender ?? "",
                Breed = metadata?.Breed ?? "Maine Coon",
                Personality = metadata?.Personality ?? "",
                FileFormat = Path.GetExtension(s3Key).TrimStart('.').ToLower(),
                FileSize = 0 // Not available without S3 API call
            };
        }

        private string ExtractCatNameFromPath(string s3Key)
        {
            // Remove key prefix first, then parse
            var relativePath = _s3StorageService?.RemoveKeyPrefix(s3Key) ?? s3Key;
            var parts = relativePath.Split('/');
            if (parts.Length >= 2)
            {
                var catName = parts[1];
                return char.ToUpper(catName[0]) + catName.Substring(1).ToLower();
            }
            return "Maine Coon Cat";
        }

        private DateTime ParseDateTaken(string? dateString)
        {
            if (string.IsNullOrEmpty(dateString))
            {
                return DateTime.UtcNow;
            }

            if (DateTime.TryParse(dateString, out var date))
            {
                return date;
            }

            return DateTime.UtcNow;
        }

        private float ParseAge(string? ageString)
        {
            if (string.IsNullOrEmpty(ageString))
            {
                return 0;
            }

            if (float.TryParse(ageString, out var age))
            {
                return age;
            }

            return 0;
        }

        private bool IsImageFile(string fileName)
        {
            var imageExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp" };
            var extension = Path.GetExtension(fileName).ToLower();
            return imageExtensions.Contains(extension);
        }
    }
}
