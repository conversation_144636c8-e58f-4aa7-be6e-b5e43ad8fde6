using System.Text.Json;
using Microsoft.Extensions.Configuration;
using VaultSharp;
using VaultSharp.V1.AuthMethods.Token;
using VaultSharp.V1.Commons;

public interface ISecretsManagerService
{
    Task<AppSecrets> GetAppSecretsAsync();
    Task<string> GetSecretAsync(string secretName);
}

public class SecretsManagerService : ISecretsManagerService
{
    private IVaultClient? _vaultClient;
    private readonly IConfiguration _configuration;
    private readonly IWebHostEnvironment _environment;
    private readonly ILogger<SecretsManagerService> _logger;
    private AppSecrets? _cachedSecrets;

    public SecretsManagerService(
        IConfiguration configuration,
        IWebHostEnvironment environment,
        ILogger<SecretsManagerService> logger)
    {
        _configuration = configuration;
        _environment = environment;
        _logger = logger;
    }

    private IVaultClient GetVaultClient()
    {
        if (_vaultClient != null)
        {
            return _vaultClient;
        }

        string vaultAddress = _configuration["Vault:Address"];
        if (string.IsNullOrWhiteSpace(vaultAddress))
        {
            throw new InvalidOperationException("Vault address is not configured.");
        }

        string vaultToken = _configuration["Vault:Token"] ?? Environment.GetEnvironmentVariable("VAULT_TOKEN");
        if (string.IsNullOrWhiteSpace(vaultToken))
        {
            throw new InvalidOperationException("Vault token is not configured.");
        }

        var authMethod = new TokenAuthMethodInfo(vaultToken);
        var vaultClientSettings = new VaultClientSettings(vaultAddress, authMethod);
        _vaultClient = new VaultClient(vaultClientSettings);
        return _vaultClient;
    }
    
    public async Task<AppSecrets> GetAppSecretsAsync()
    {
        if (_cachedSecrets != null)
            return _cachedSecrets;

        // Check if we're running in secure Docker mode (secrets mounted as files)
        if (Directory.Exists("/run/secrets"))
        {
            _logger.LogInformation("Loading secrets from Docker secrets files");
            return await LoadSecretsFromFilesAsync();
        }

        if (_environment.IsDevelopment())
        {
            // In development, use appsettings values
            _cachedSecrets = new AppSecrets
            {
                DbConnectionString = _configuration.GetConnectionString("DefaultConnection"),
                JwtSecret = _configuration["JwtSettings:Secret"],
                JwtIssuer = _configuration["JwtSettings:Issuer"],
                JwtAudience = _configuration["JwtSettings:Audience"],
                JwtExpiryMinutes = int.Parse(_configuration["JwtSettings:ExpiryMinutes"] ?? "60"),
                RefreshExpiryDays = int.Parse(_configuration["JwtSettings:RefreshExpiryDays"] ?? "7"),

                // For development, you can set these in user secrets or environment variables
                S3AccessKey = Environment.GetEnvironmentVariable("AWS_ACCESS_KEY_ID"),
                S3SecretKey = Environment.GetEnvironmentVariable("AWS_SECRET_ACCESS_KEY"),
                S3SessionToken = Environment.GetEnvironmentVariable("AWS_SESSION_TOKEN")
            };
            return _cachedSecrets;
        }

        try
        {
            var vaultClient = GetVaultClient();
            // In production, get from HashiCorp Vault
            string secretPath = _configuration["Vault:SecretPath"] ?? "secret/yendorcats/app-secrets";
            var secret = await vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync(secretPath);
            string secretJson = JsonSerializer.Serialize(secret.Data.Data);
            _cachedSecrets = JsonSerializer.Deserialize<AppSecrets>(secretJson) ?? new AppSecrets();
            return _cachedSecrets;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve secrets from HashiCorp Vault");
            throw;
        }
    }

    public async Task<string> GetSecretAsync(string secretName)
    {
        // Check if we're running in secure Docker mode (secrets mounted as files)
        if (Directory.Exists("/run/secrets"))
        {
            var secretPath = Path.Combine("/run/secrets", secretName);
            if (File.Exists(secretPath))
            {
                return await File.ReadAllTextAsync(secretPath);
            }
        }

        if (_environment.IsDevelopment() && secretName == "yendorcats/app-secrets")
        {
            // Mock secret in development to avoid costs
            return JsonSerializer.Serialize(new AppSecrets
            {
                DbConnectionString = _configuration.GetConnectionString("DefaultConnection"),
                JwtSecret = _configuration["JwtSettings:Secret"],
                JwtIssuer = _configuration["JwtSettings:Issuer"],
                JwtAudience = _configuration["JwtSettings:Audience"],
                JwtExpiryMinutes = int.Parse(_configuration["JwtSettings:ExpiryMinutes"] ?? "60"),
                RefreshExpiryDays = int.Parse(_configuration["JwtSettings:RefreshExpiryDays"] ?? "7"),

                // For development, you can set these in user secrets or environment variables
                S3AccessKey = Environment.GetEnvironmentVariable("AWS_ACCESS_KEY_ID"),
                S3SecretKey = Environment.GetEnvironmentVariable("AWS_SECRET_ACCESS_KEY"),
                S3SessionToken = Environment.GetEnvironmentVariable("AWS_SESSION_TOKEN")
            });
        }

        try
        {
            var vaultClient = GetVaultClient();
            // In production, get from HashiCorp Vault
            string secretPath = secretName.StartsWith("secret/") ? secretName : $"secret/{secretName}";
            var secret = await vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync(secretPath);
            return JsonSerializer.Serialize(secret.Data.Data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error retrieving secret {secretName} from HashiCorp Vault");
            throw;
        }
    }

    private async Task<AppSecrets> LoadSecretsFromFilesAsync()
    {
        var secrets = new AppSecrets();

        // Load JWT secrets
        secrets.JwtSecret = await ReadSecretFromFileAsync("jwt_secret");
        secrets.JwtIssuer = _configuration["JwtSettings:Issuer"] ?? "YendorCatsApi";
        secrets.JwtAudience = _configuration["JwtSettings:Audience"] ?? "YendorCatsClients";
        secrets.JwtExpiryMinutes = int.Parse(_configuration["JwtSettings:ExpiryMinutes"] ?? "60");
        secrets.RefreshExpiryDays = int.Parse(_configuration["JwtSettings:RefreshExpiryDays"] ?? "7");

        // Load AWS S3 secrets
        secrets.S3AccessKey = await ReadSecretFromFileAsync("aws_s3_access_key");
        secrets.S3SecretKey = await ReadSecretFromFileAsync("aws_s3_secret_key");

        // Load B2 secrets
        var b2KeyId = await ReadSecretFromFileAsync("b2_application_key_id");
        var b2Key = await ReadSecretFromFileAsync("b2_application_key");
        var b2BucketId = await ReadSecretFromFileAsync("b2_bucket_id");

        // For database, we'll still use environment variables for now
        // In a more secure setup, these would also be loaded from files
        secrets.DbConnectionString = _configuration.GetConnectionString("DefaultConnection") ??
            $"Server=db;Database=YendorCats;User={await ReadSecretFromFileAsync("mysql_user")};Password={await ReadSecretFromFileAsync("mysql_password")};Port=3306;";

        return secrets;
    }

    private async Task<string> ReadSecretFromFileAsync(string secretName)
    {
        try
        {
            var secretPath = Path.Combine("/run/secrets", secretName);
            if (File.Exists(secretPath))
            {
                var content = await File.ReadAllTextAsync(secretPath);
                return content.Trim();
            }
            else
            {
                _logger.LogWarning("Secret file not found: {SecretPath}", secretPath);
                return string.Empty;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading secret from file: {SecretName}", secretName);
            return string.Empty;
        }
    }
}
