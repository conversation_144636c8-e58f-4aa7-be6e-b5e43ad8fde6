using Microsoft.EntityFrameworkCore;
using YendorCats.API.Data;
using YendorCats.API.Models;
using Amazon.S3.Model;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Service for periodic metadata synchronization between B2 bucket and database
    /// </summary>
    public interface IMetadataSyncService
    {
        /// <summary>
        /// Sync metadata from B2 bucket to database
        /// </summary>
        /// <returns>Sync result with statistics</returns>
        Task<MetadataSyncResult> SyncMetadataAsync();

        /// <summary>
        /// Trigger immediate metadata refresh
        /// </summary>
        /// <param name="category">Optional specific category to sync</param>
        /// <returns>Sync result</returns>
        Task<MetadataSyncResult> TriggerImmediateSyncAsync(string? category = null);

        /// <summary>
        /// Force re-sync metadata, overwriting existing database entries with S3 metadata
        /// </summary>
        /// <param name="category">Optional specific category to force sync</param>
        /// <param name="overwriteFallbackData">Whether to overwrite existing fallback data</param>
        /// <returns>Sync result</returns>
        Task<MetadataSyncResult> ForceResyncMetadataAsync(string? category = null, bool overwriteFallbackData = true);

        /// <summary>
        /// Get the last sync status
        /// </summary>
        /// <returns>Last sync information</returns>
        Task<MetadataSyncStatus> GetLastSyncStatusAsync();
    }

    /// <summary>
    /// Implementation of metadata sync service
    /// </summary>
    public class MetadataSyncService : IMetadataSyncService
    {
        private readonly ILogger<MetadataSyncService> _logger;
        private readonly IS3StorageService _s3StorageService;
        private readonly AppDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly SemaphoreSlim _syncSemaphore = new(1, 1);
        private static DateTime _lastSyncTime = DateTime.MinValue;

        public MetadataSyncService(
            ILogger<MetadataSyncService> logger,
            IS3StorageService s3StorageService,
            AppDbContext context,
            IConfiguration configuration)
        {
            _logger = logger;
            _s3StorageService = s3StorageService;
            _context = context;
            _configuration = configuration;
        }

        public async Task<MetadataSyncResult> SyncMetadataAsync()
        {
            // Check if sync is needed (every 3 minutes)
            if (DateTime.UtcNow - _lastSyncTime < TimeSpan.FromMinutes(3))
            {
                _logger.LogDebug("Metadata sync not needed, last sync was recent");
                return new MetadataSyncResult
                {
                    Success = true,
                    Message = "Sync not needed - recent sync available",
                    SyncTime = _lastSyncTime
                };
            }

            return await TriggerImmediateSyncAsync();
        }

        public async Task<MetadataSyncResult> TriggerImmediateSyncAsync(string? category = null)
        {
            if (!await _syncSemaphore.WaitAsync(TimeSpan.FromSeconds(5)))
            {
                return new MetadataSyncResult
                {
                    Success = false,
                    Message = "Another sync operation is already in progress"
                };
            }

            try
            {
                _logger.LogInformation("Starting metadata sync {Category}", 
                    string.IsNullOrEmpty(category) ? "for all categories" : $"for category: {category}");

                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                var result = new MetadataSyncResult
                {
                    SyncTime = DateTime.UtcNow
                };

                var categories = string.IsNullOrEmpty(category) 
                    ? new[] { "studs", "queens", "kittens", "gallery" }
                    : new[] { category };

                foreach (var cat in categories)
                {
                    try
                    {
                        var categoryResult = await SyncCategoryMetadata(cat);
                        result.CategoriesProcessed++;
                        result.ImagesProcessed += categoryResult.ImagesProcessed;
                        result.ImagesAdded += categoryResult.ImagesAdded;
                        result.ImagesUpdated += categoryResult.ImagesUpdated;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error syncing category: {Category}", cat);
                        result.Errors.Add($"Category {cat}: {ex.Message}");
                    }
                }

                stopwatch.Stop();
                result.Duration = stopwatch.Elapsed;
                result.Success = result.Errors.Count == 0;
                result.Message = result.Success 
                    ? $"Sync completed successfully. Processed {result.ImagesProcessed} images."
                    : $"Sync completed with {result.Errors.Count} errors.";

                _lastSyncTime = result.SyncTime;

                _logger.LogInformation("Metadata sync completed: {Success}, Duration: {Duration}ms, Images: {Images}", 
                    result.Success, result.Duration.TotalMilliseconds, result.ImagesProcessed);

                return result;
            }
            finally
            {
                _syncSemaphore.Release();
            }
        }

        public async Task<MetadataSyncStatus> GetLastSyncStatusAsync()
        {
            return new MetadataSyncStatus
            {
                LastSyncTime = _lastSyncTime,
                IsRecentSync = DateTime.UtcNow - _lastSyncTime < TimeSpan.FromMinutes(3),
                NextScheduledSync = _lastSyncTime.AddMinutes(3),
                DatabaseImageCount = await _context.CatGalleryImages.CountAsync()
            };
        }

        public async Task<MetadataSyncResult> ForceResyncMetadataAsync(string? category = null, bool overwriteFallbackData = true)
        {
            if (!await _syncSemaphore.WaitAsync(TimeSpan.FromSeconds(10)))
            {
                return new MetadataSyncResult
                {
                    Success = false,
                    Message = "Another sync operation is already in progress"
                };
            }

            try
            {
                _logger.LogInformation("Starting FORCE metadata re-sync {Category} (overwrite fallback: {OverwriteFallback})",
                    string.IsNullOrEmpty(category) ? "for all categories" : $"for category: {category}", overwriteFallbackData);

                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                var result = new MetadataSyncResult
                {
                    SyncTime = DateTime.UtcNow
                };

                var categories = string.IsNullOrEmpty(category)
                    ? new[] { "studs", "queens", "kittens", "gallery" }
                    : new[] { category };

                foreach (var cat in categories)
                {
                    try
                    {
                        var categoryResult = await ForceResyncCategoryMetadata(cat, overwriteFallbackData);
                        result.CategoriesProcessed++;
                        result.ImagesProcessed += categoryResult.ImagesProcessed;
                        result.ImagesAdded += categoryResult.ImagesAdded;
                        result.ImagesUpdated += categoryResult.ImagesUpdated;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error force re-syncing category: {Category}", cat);
                        result.Errors.Add($"Category {cat}: {ex.Message}");
                    }
                }

                stopwatch.Stop();
                result.Duration = stopwatch.Elapsed;
                result.Success = result.Errors.Count == 0;
                result.Message = result.Success
                    ? $"Force re-sync completed successfully. Updated {result.ImagesUpdated} images with S3 metadata."
                    : $"Force re-sync completed with {result.Errors.Count} errors.";

                _lastSyncTime = result.SyncTime;

                _logger.LogInformation("Force metadata re-sync completed: {Success}, Duration: {Duration}ms, Updated: {Updated}",
                    result.Success, result.Duration.TotalMilliseconds, result.ImagesUpdated);

                return result;
            }
            finally
            {
                _syncSemaphore.Release();
            }
        }

        private async Task<CategorySyncResult> SyncCategoryMetadata(string category)
        {
            _logger.LogDebug("Syncing metadata for category: {Category}", category);

            var result = new CategorySyncResult { Category = category };

            try
            {
                // Get current images from database for this category
                var existingImages = await _context.CatGalleryImages
                    .Where(img => img.Category.ToLower() == category.ToLower())
                    .ToListAsync();

                var existingKeys = existingImages.ToDictionary(img => img.StorageKey ?? "", img => img);

                // List objects in B2 bucket for this category
                var b2Objects = await _s3StorageService.ListFilesAsync($"{category}/");

                foreach (var obj in b2Objects)
                {
                    result.ImagesProcessed++;

                    // Skip non-image files
                    if (!IsImageFile(obj.Key))
                        continue;

                    if (existingKeys.ContainsKey(obj.Key))
                    {
                        // Update existing image if needed
                        var existingImage = existingKeys[obj.Key];
                        bool needsUpdate = false;

                        if (existingImage.FileSize != obj.Size)
                        {
                            existingImage.FileSize = obj.Size;
                            needsUpdate = true;
                        }

                        if (existingImage.DateModified != obj.LastModified)
                        {
                            existingImage.DateModified = obj.LastModified;
                            needsUpdate = true;
                        }

                        if (needsUpdate)
                        {
                            result.ImagesUpdated++;
                        }
                    }
                    else
                    {
                        // Add new image
                        var newImage = await CreateImageMetadataFromS3Object(obj, category);
                        if (newImage != null)
                        {
                            _context.CatGalleryImages.Add(newImage);
                            result.ImagesAdded++;
                        }
                    }
                }

                // Save changes for this category
                await _context.SaveChangesAsync();

                _logger.LogDebug("Category {Category} sync completed: {Added} added, {Updated} updated", 
                    category, result.ImagesAdded, result.ImagesUpdated);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing category {Category}", category);
                throw;
            }
        }

        private async Task<CatGalleryImage?> CreateImageMetadataFromS3Object(Amazon.S3.Model.S3Object obj, string category)
        {
            try
            {
                // Remove the key prefix to get the relative path
                var relativePath = _s3StorageService.RemoveKeyPrefix(obj.Key);
                var pathParts = relativePath.Split('/');
                string catName;
                string filename;

                // Handle both direct category images and cat subfolder images
                if (pathParts.Length == 2)
                {
                    // Direct in category: queens/IMG_4406.jpg
                    filename = pathParts[1];
                    catName = ExtractCatNameFromFilename(filename, category);
                }
                else if (pathParts.Length == 3)
                {
                    // In cat subfolder: queens/Cat1/TESTING-IMG_4275.jpg or studs/Dennis/image.jpg
                    catName = pathParts[1];
                    filename = pathParts[2];

                    // Also try to extract from filename if folder name looks generic
                    if (catName.ToLower().Contains("cat") || catName.ToLower() == "general")
                    {
                        var extractedName = ExtractCatNameFromFilename(filename, category);
                        if (!string.IsNullOrEmpty(extractedName) && extractedName != catName)
                        {
                            catName = extractedName;
                        }
                    }
                }
                else
                {
                    _logger.LogWarning("Unexpected S3 object path format: {Key} (relative: {RelativePath})", obj.Key, relativePath);
                    return null;
                }

                // Skip .bzEmpty files and other non-image files
                if (filename == ".bzEmpty" || !IsImageFile(obj.Key))
                {
                    return null;
                }

                // PRIORITY SYSTEM: Check S3 custom metadata first, then fallback methods
                var s3Metadata = await _s3StorageService.GetObjectMetadataAsync(obj.Key);
                var metadataExtractedInfo = ExtractMetadataFromS3(s3Metadata, catName, category);
                
                // Update catName if found in S3 metadata (this has highest priority)
                if (!string.IsNullOrEmpty(metadataExtractedInfo.CatName))
                {
                    catName = metadataExtractedInfo.CatName;
                }
                else
                {
                    // Clean up cat name from filename extraction
                    catName = CleanCatName(catName);
                }

                // Generate CatId and ensure cat profile exists
                var catId = $"{category}-{catName}".ToLower();
                await EnsureCatProfileExists(catId, catName, category);

                // Generate public URL with proper key prefix handling
                var publicUrlTemplate = _configuration["AWS:S3:PublicUrl"] ?? "https://f004.backblazeb2.com/file/yendor/{key}";
                var publicUrl = publicUrlTemplate.Replace("{key}", obj.Key);

                // Generate unique ID from S3 key hash (more reliable than GUID conversion)
                var hashCode = Math.Abs(obj.Key.GetHashCode());
                var uniqueId = hashCode % **********; // Keep within reasonable int range

                // Create image with S3 metadata priority
                var image = new CatGalleryImage
                {
                    CatId = catId,
                    CatName = catName,
                    Category = category,
                    StorageKey = obj.Key,
                    StorageProvider = "S3",
                    StorageBucketName = "yendor",
                    S3Key = obj.Key,
                    S3Bucket = "yendor",
                    OriginalFileName = filename,
                    Filename = filename,
                    ImageUrl = publicUrl,
                    S3Url = publicUrl,
                    FileSize = obj.Size,
                    Width = 800, // Default width - could be determined from actual image
                    Height = 600, // Default height - could be determined from actual image
                    ContentType = GetMimeType(filename),
                    MimeType = GetMimeType(filename),
                    Format = Path.GetExtension(filename).TrimStart('.').ToLowerInvariant(),
                    FileFormat = Path.GetExtension(filename).TrimStart('.').ToLowerInvariant(),
                    DateModified = obj.LastModified,
                    DateTaken = metadataExtractedInfo.DateTaken ?? obj.LastModified,
                    DateUploaded = obj.LastModified,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    DisplayOrder = 0,
                    SortOrder = 0,
                    Age = metadataExtractedInfo.Age ?? 0,
                    IsActive = true,
                    IsPublic = true,
                    IsFeatured = false,
                    Breed = metadataExtractedInfo.Breed ?? "Maine Coon", // Use S3 metadata breed if available
                    ViewCount = 0,
                    AccessCount = 0,
                    LikeCount = 0,
                    DownloadCount = 0,
                    // Additional S3 metadata fields
                    Description = metadataExtractedInfo.Description,
                    Tags = metadataExtractedInfo.Tags,
                    Gender = metadataExtractedInfo.Gender
                };

                return image;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating image metadata for S3 object: {Key}", obj.Key);
                return null;
            }
        }

        private string ExtractCatNameFromFilename(string filename, string category)
        {
            try
            {
                // Remove file extension
                var nameWithoutExt = Path.GetFileNameWithoutExtension(filename);
                
                // Remove common prefixes and patterns
                nameWithoutExt = nameWithoutExt.Replace("TESTING-", "");
                nameWithoutExt = nameWithoutExt.Replace("IMG_", "");
                nameWithoutExt = nameWithoutExt.Replace("DSC_", "");
                
                // Split by common delimiters and take the first meaningful part
                var parts = nameWithoutExt.Split(new[] { '-', '_', ' ' }, StringSplitOptions.RemoveEmptyEntries);
                
                foreach (var part in parts)
                {
                    // Skip numeric-only parts and common camera codes
                    if (part.All(char.IsDigit) || part.Length < 3)
                        continue;
                        
                    // Skip date patterns
                    if (System.Text.RegularExpressions.Regex.IsMatch(part, @"^\d{6}$|^\d{8}$"))
                        continue;
                        
                    // This looks like a name
                    return CapitalizeName(part);
                }
                
                // Fallback: use first part if no meaningful name found
                if (parts.Length > 0 && !parts[0].All(char.IsDigit))
                {
                    return CapitalizeName(parts[0]);
                }
                
                return $"Beautiful {category.TrimEnd('s')}"; // Remove 's' from category
            }
            catch
            {
                return $"Beautiful {category.TrimEnd('s')}";
            }
        }

        private string CleanCatName(string catName)
        {
            if (string.IsNullOrWhiteSpace(catName))
                return "Unknown";
                
            // Remove unwanted characters and normalize
            catName = catName.Trim().Replace("_", " ").Replace("-", " ");
            
            // Remove multiple spaces
            while (catName.Contains("  "))
                catName = catName.Replace("  ", " ");
                
            return CapitalizeName(catName);
        }

        private string CapitalizeName(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return "Unknown";
                
            var words = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            for (int i = 0; i < words.Length; i++)
            {
                if (words[i].Length > 0)
                {
                    words[i] = char.ToUpper(words[i][0]) + words[i].Substring(1).ToLower();
                }
            }
            return string.Join(" ", words);
        }

        private async Task EnsureCatProfileExists(string catId, string catName, string category)
        {
            try
            {
                // Check if cat profile already exists
                var existingProfile = await _context.CatProfiles
                    .FirstOrDefaultAsync(cp => cp.CatId == catId);

                if (existingProfile == null)
                {
                    // Create placeholder cat profile
                    var catProfile = new CatProfile
                    {
                        CatId = catId,
                        CatName = catName,
                        Breed = "Maine Coon",
                        Gender = category == "studs" ? "M" : category == "queens" ? "F" : "U", // U for Unknown/Undetermined
                        CreatedAt = DateTime.UtcNow
                    };

                    _context.CatProfiles.Add(catProfile);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Created placeholder cat profile: {CatId} for {CatName}", catId, catName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error ensuring cat profile exists for {CatId}", catId);
                throw;
            }
        }

        private string GetMimeType(string filename)
        {
            var extension = Path.GetExtension(filename).ToLowerInvariant();
            return extension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".webp" => "image/webp",
                ".bmp" => "image/bmp",
                _ => "image/jpeg"
            };
        }

        private bool IsImageFile(string key)
        {
            var extension = Path.GetExtension(key).ToLower();
            return new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp" }.Contains(extension);
        }

        /// <summary>
        /// Extract metadata information from S3 custom metadata headers
        /// Priority: S3 metadata > Fallback methods
        /// </summary>
        private (string CatName, string Breed, string Description, string Tags, string Gender, DateTime? DateTaken, int? Age) ExtractMetadataFromS3(Dictionary<string, string> s3Metadata, string fallbackCatName, string category)
        {
            string catName = null;
            string breed = null;
            string description = null;
            string tags = null;
            string gender = null;
            DateTime? dateTaken = null;
            int? age = null;

            if (s3Metadata != null && s3Metadata.Count > 0)
            {
                _logger.LogDebug("Processing S3 metadata for image: {MetadataKeys}", string.Join(", ", s3Metadata.Keys));

                // Extract cat name (highest priority metadata)
                catName = GetMetadataValue(s3Metadata, "cat-name", "name", "catname");
                
                // Extract other metadata fields
                breed = GetMetadataValue(s3Metadata, "breed", "cat-breed");
                description = GetMetadataValue(s3Metadata, "description", "desc");
                tags = GetMetadataValue(s3Metadata, "tags", "keywords");
                gender = GetMetadataValue(s3Metadata, "gender", "sex");

                // Parse date taken
                var dateTakenStr = GetMetadataValue(s3Metadata, "date-taken", "photo-date", "taken-date");
                if (!string.IsNullOrEmpty(dateTakenStr) && DateTime.TryParse(dateTakenStr, out var parsedDate))
                {
                    dateTaken = parsedDate;
                }

                // Parse age
                var ageStr = GetMetadataValue(s3Metadata, "age", "cat-age", "age-at-photo");
                if (!string.IsNullOrEmpty(ageStr))
                {
                    if (int.TryParse(ageStr, out var parsedAge))
                    {
                        age = parsedAge;
                    }
                    else
                    {
                        // Try to parse age from strings like "6 months", "2 years"
                        age = ParseAgeFromString(ageStr);
                    }
                }

                // Log what we found in S3 metadata
                if (!string.IsNullOrEmpty(catName))
                {
                    _logger.LogInformation("Found cat name in S3 metadata: '{CatName}' (was going to use fallback: '{FallbackName}')",
                        catName, fallbackCatName);
                }
            }

            return (catName, breed, description, tags, gender, dateTaken, age);
        }

        /// <summary>
        /// Get metadata value by checking multiple possible keys (case-insensitive)
        /// </summary>
        private string GetMetadataValue(Dictionary<string, string> metadata, params string[] possibleKeys)
        {
            foreach (var key in possibleKeys)
            {
                // Check exact match first
                if (metadata.TryGetValue(key, out var value) && !string.IsNullOrWhiteSpace(value))
                    return value.Trim();

                // Check case-insensitive match
                var kvp = metadata.FirstOrDefault(m => string.Equals(m.Key, key, StringComparison.OrdinalIgnoreCase));
                if (!string.IsNullOrEmpty(kvp.Key) && !string.IsNullOrWhiteSpace(kvp.Value))
                    return kvp.Value.Trim();
            }
            return null;
        }

        /// <summary>
        /// Parse age from string descriptions like "6 months", "2 years", "newborn"
        /// </summary>
        private int? ParseAgeFromString(string ageStr)
        {
            if (string.IsNullOrWhiteSpace(ageStr))
                return null;

            ageStr = ageStr.ToLower().Trim();

            // Handle specific age descriptions
            if (ageStr.Contains("newborn") || ageStr.Contains("0 weeks"))
                return 0;

            if (ageStr.Contains("kitten"))
                return 3; // Default kitten age in months

            // Try to extract numeric value
            var numbers = System.Text.RegularExpressions.Regex.Matches(ageStr, @"\d+");
            if (numbers.Count > 0 && int.TryParse(numbers[0].Value, out var numericAge))
            {
                // Convert years to months
                if (ageStr.Contains("year"))
                    return numericAge * 12;

                // Weeks to months (approximate)
                if (ageStr.Contains("week"))
                    return (int)Math.Ceiling(numericAge / 4.0);

                // Assume months if no unit specified
                return numericAge;
            }

            return null;
        }

        /// <summary>
        /// Force re-sync category metadata, specifically designed to overwrite fallback data
        /// </summary>
        private async Task<CategorySyncResult> ForceResyncCategoryMetadata(string category, bool overwriteFallbackData)
        {
            _logger.LogDebug("Force re-syncing metadata for category: {Category} (overwrite fallback: {OverwriteFallback})",
                category, overwriteFallbackData);

            var result = new CategorySyncResult { Category = category };

            try
            {
                // Get current images from database for this category
                var existingImages = await _context.CatGalleryImages
                    .Where(img => img.Category.ToLower() == category.ToLower())
                    .ToListAsync();

                var existingKeys = existingImages.ToDictionary(img => img.StorageKey ?? "", img => img);

                // List objects in B2 bucket for this category
                var b2Objects = await _s3StorageService.ListFilesAsync($"{category}/");

                foreach (var obj in b2Objects)
                {
                    result.ImagesProcessed++;

                    // Skip non-image files
                    if (!IsImageFile(obj.Key))
                        continue;

                    if (existingKeys.ContainsKey(obj.Key))
                    {
                        var existingImage = existingKeys[obj.Key];
                        
                        // Check if this looks like fallback data that should be updated
                        bool shouldUpdate = overwriteFallbackData && IsLikelyFallbackData(existingImage);
                        
                        // Always update if basic file metadata changed
                        if (existingImage.FileSize != obj.Size || existingImage.DateModified != obj.LastModified)
                        {
                            shouldUpdate = true;
                        }

                        if (shouldUpdate)
                        {
                            // Get S3 metadata and update the database entry
                            var s3Metadata = await _s3StorageService.GetObjectMetadataAsync(obj.Key);
                            var metadataInfo = ExtractMetadataFromS3(s3Metadata, existingImage.CatName, category);
                            
                            // Update database entry with S3 metadata (if any found)
                            bool actuallyUpdated = UpdateImageWithS3Metadata(existingImage, obj, metadataInfo);
                            
                            if (actuallyUpdated)
                            {
                                existingImage.UpdatedAt = DateTime.UtcNow;
                                result.ImagesUpdated++;
                                
                                _logger.LogInformation("Updated existing image {Key} with S3 metadata: CatName='{CatName}'",
                                    obj.Key, existingImage.CatName);
                            }
                        }
                    }
                    else
                    {
                        // Add new image (same as normal sync)
                        var newImage = await CreateImageMetadataFromS3Object(obj, category);
                        if (newImage != null)
                        {
                            _context.CatGalleryImages.Add(newImage);
                            result.ImagesAdded++;
                        }
                    }
                }

                // Save changes for this category
                await _context.SaveChangesAsync();

                _logger.LogDebug("Force re-sync category {Category} completed: {Added} added, {Updated} updated",
                    category, result.ImagesAdded, result.ImagesUpdated);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error force re-syncing category {Category}", category);
                throw;
            }
        }

        /// <summary>
        /// Check if image data looks like fallback data that should be overwritten
        /// </summary>
        private bool IsLikelyFallbackData(CatGalleryImage image)
        {
            // Check for fallback cat names
            if (!string.IsNullOrEmpty(image.CatName))
            {
                var lowerName = image.CatName.ToLower();
                if (lowerName.Contains("beautiful queen") ||
                    lowerName.Contains("beautiful stud") ||
                    lowerName.Contains("beautiful kitten") ||
                    lowerName.StartsWith("img_") ||
                    lowerName.StartsWith("dsc_"))
                {
                    return true;
                }
            }

            // Check if breed is just the default
            if (image.Breed == "Maine Coon" && string.IsNullOrEmpty(image.Description) &&
                string.IsNullOrEmpty(image.Tags) && string.IsNullOrEmpty(image.Gender))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Update image object with S3 metadata information
        /// Returns true if any updates were actually made
        /// </summary>
        private bool UpdateImageWithS3Metadata(CatGalleryImage image, Amazon.S3.Model.S3Object obj,
            (string CatName, string Breed, string Description, string Tags, string Gender, DateTime? DateTaken, int? Age) metadataInfo)
        {
            bool updated = false;

            // Update cat name if found in S3 metadata
            if (!string.IsNullOrEmpty(metadataInfo.CatName) && metadataInfo.CatName != image.CatName)
            {
                image.CatName = metadataInfo.CatName;
                updated = true;
            }

            // Update breed if found in S3 metadata
            if (!string.IsNullOrEmpty(metadataInfo.Breed) && metadataInfo.Breed != image.Breed)
            {
                image.Breed = metadataInfo.Breed;
                updated = true;
            }

            // Update description if found in S3 metadata
            if (!string.IsNullOrEmpty(metadataInfo.Description) && metadataInfo.Description != image.Description)
            {
                image.Description = metadataInfo.Description;
                updated = true;
            }

            // Update tags if found in S3 metadata
            if (!string.IsNullOrEmpty(metadataInfo.Tags) && metadataInfo.Tags != image.Tags)
            {
                image.Tags = metadataInfo.Tags;
                updated = true;
            }

            // Update gender if found in S3 metadata
            if (!string.IsNullOrEmpty(metadataInfo.Gender) && metadataInfo.Gender != image.Gender)
            {
                image.Gender = metadataInfo.Gender;
                updated = true;
            }

            // Update date taken if found in S3 metadata
            if (metadataInfo.DateTaken.HasValue && metadataInfo.DateTaken != image.DateTaken)
            {
                image.DateTaken = metadataInfo.DateTaken.Value;
                updated = true;
            }

            // Update age if found in S3 metadata
            if (metadataInfo.Age.HasValue && metadataInfo.Age != image.Age)
            {
                image.Age = metadataInfo.Age.Value;
                updated = true;
            }

            // Always update basic file metadata
            if (image.FileSize != obj.Size)
            {
                image.FileSize = obj.Size;
                updated = true;
            }

            if (image.DateModified != obj.LastModified)
            {
                image.DateModified = obj.LastModified;
                updated = true;
            }

            return updated;
        }
    }

    /// <summary>
    /// Result of metadata sync operation
    /// </summary>
    public class MetadataSyncResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime SyncTime { get; set; }
        public TimeSpan Duration { get; set; }
        public int CategoriesProcessed { get; set; }
        public int ImagesProcessed { get; set; }
        public int ImagesAdded { get; set; }
        public int ImagesUpdated { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    /// <summary>
    /// Status of metadata synchronization
    /// </summary>
    public class MetadataSyncStatus
    {
        public DateTime LastSyncTime { get; set; }
        public bool IsRecentSync { get; set; }
        public DateTime NextScheduledSync { get; set; }
        public int DatabaseImageCount { get; set; }
    }

    /// <summary>
    /// Result for a single category sync
    /// </summary>
    internal class CategorySyncResult
    {
        public string Category { get; set; } = string.Empty;
        public int ImagesProcessed { get; set; }
        public int ImagesAdded { get; set; }
        public int ImagesUpdated { get; set; }
    }

    /// <summary>
    /// Information about a B2 object
    /// </summary>
    public class B2ObjectInfo
    {
        public string Key { get; set; } = string.Empty;
        public long Size { get; set; }
        public DateTime LastModified { get; set; }
        public string ETag { get; set; } = string.Empty;
    }
}
