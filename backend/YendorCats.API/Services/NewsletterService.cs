using System.Text.Json;
using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Service for managing newsletter subscriptions using JSON file storage
    /// </summary>
    public class NewsletterService : INewsletterService
    {
        private readonly ILogger<NewsletterService> _logger;
        private readonly string _dataFilePath;
        private readonly SemaphoreSlim _fileLock = new(1, 1);

        /// <summary>
        /// Constructor for newsletter service
        /// </summary>
        /// <param name="logger">Logger instance</param>
        /// <param name="webHostEnvironment">Web host environment for file paths</param>
        public NewsletterService(ILogger<NewsletterService> logger, IWebHostEnvironment webHostEnvironment)
        {
            _logger = logger;
            _dataFilePath = Path.Combine(webHostEnvironment.ContentRootPath, "Data", "NewsletterSubscriptions.json");
            
            // Ensure the Data directory exists
            var dataDirectory = Path.GetDirectoryName(_dataFilePath);
            if (!Directory.Exists(dataDirectory))
            {
                Directory.CreateDirectory(dataDirectory!);
            }
        }

        /// <summary>
        /// Add a new newsletter subscription
        /// </summary>
        /// <param name="subscription">Newsletter subscription to add</param>
        /// <returns>True if successful</returns>
        public async Task<bool> AddSubscriptionAsync(NewsletterSubscription subscription)
        {
            try
            {
                await _fileLock.WaitAsync();
                
                var subscriptions = await LoadSubscriptionsAsync();
                
                // Check for duplicate email
                if (subscriptions.Any(s => s.Email.Equals(subscription.Email, StringComparison.OrdinalIgnoreCase)))
                {
                    _logger.LogWarning("Attempted to add duplicate subscription for email: {Email}", subscription.Email);
                    return false;
                }
                
                subscription.Id = Guid.NewGuid().ToString();
                subscription.SubscribedAt = DateTime.UtcNow;
                subscription.LastUpdated = DateTime.UtcNow;
                
                subscriptions.Add(subscription);
                await SaveSubscriptionsAsync(subscriptions);
                
                _logger.LogInformation("Added new newsletter subscription for {Email} with interests {Interests}", 
                    subscription.Email, subscription.Interests ?? "none");
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding newsletter subscription for {Email}", subscription.Email);
                return false;
            }
            finally
            {
                _fileLock.Release();
            }
        }

        /// <summary>
        /// Get all newsletter subscriptions
        /// </summary>
        /// <returns>List of newsletter subscriptions</returns>
        public async Task<List<NewsletterSubscription>> GetAllSubscriptionsAsync()
        {
            try
            {
                var subscriptions = await LoadSubscriptionsAsync();
                return subscriptions.OrderByDescending(s => s.SubscribedAt).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading newsletter subscriptions");
                return new List<NewsletterSubscription>();
            }
        }

        /// <summary>
        /// Get a specific newsletter subscription by ID
        /// </summary>
        /// <param name="id">Subscription ID</param>
        /// <returns>Newsletter subscription or null if not found</returns>
        public async Task<NewsletterSubscription?> GetSubscriptionByIdAsync(string id)
        {
            try
            {
                var subscriptions = await LoadSubscriptionsAsync();
                return subscriptions.FirstOrDefault(s => s.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting newsletter subscription {Id}", id);
                return null;
            }
        }

        /// <summary>
        /// Update a newsletter subscription status
        /// </summary>
        /// <param name="id">Subscription ID</param>
        /// <param name="status">New status</param>
        /// <param name="notes">Admin notes</param>
        /// <param name="updatedBy">Username of who updated it</param>
        /// <returns>True if successful</returns>
        public async Task<bool> UpdateSubscriptionStatusAsync(string id, string status, string? notes, string updatedBy)
        {
            try
            {
                await _fileLock.WaitAsync();
                
                var subscriptions = await LoadSubscriptionsAsync();
                var subscription = subscriptions.FirstOrDefault(s => s.Id == id);
                
                if (subscription == null)
                {
                    _logger.LogWarning("Newsletter subscription {Id} not found for update", id);
                    return false;
                }
                
                subscription.Status = status;
                subscription.Notes = notes;
                subscription.LastUpdated = DateTime.UtcNow;
                subscription.LastUpdatedBy = updatedBy;
                
                await SaveSubscriptionsAsync(subscriptions);
                
                _logger.LogInformation("Updated newsletter subscription {Id} status to {Status} by {UpdatedBy}", 
                    id, status, updatedBy);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating newsletter subscription {Id}", id);
                return false;
            }
            finally
            {
                _fileLock.Release();
            }
        }

        /// <summary>
        /// Check if an email is already subscribed
        /// </summary>
        /// <param name="email">Email address to check</param>
        /// <returns>True if email is already subscribed</returns>
        public async Task<bool> IsEmailSubscribedAsync(string email)
        {
            try
            {
                var subscriptions = await LoadSubscriptionsAsync();
                return subscriptions.Any(s => s.Email.Equals(email, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking email subscription status for {Email}", email);
                return false;
            }
        }

        /// <summary>
        /// Get subscriptions by status
        /// </summary>
        /// <param name="status">Status to filter by</param>
        /// <returns>List of matching subscriptions</returns>
        public async Task<List<NewsletterSubscription>> GetSubscriptionsByStatusAsync(string status)
        {
            try
            {
                var subscriptions = await LoadSubscriptionsAsync();
                return subscriptions
                    .Where(s => s.Status.Equals(status, StringComparison.OrdinalIgnoreCase))
                    .OrderByDescending(s => s.SubscribedAt)
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting subscriptions by status {Status}", status);
                return new List<NewsletterSubscription>();
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Load subscriptions from JSON file
        /// </summary>
        /// <returns>List of newsletter subscriptions</returns>
        private async Task<List<NewsletterSubscription>> LoadSubscriptionsAsync()
        {
            if (!File.Exists(_dataFilePath))
            {
                _logger.LogInformation("Newsletter subscriptions file not found, returning empty list");
                return new List<NewsletterSubscription>();
            }

            try
            {
                var jsonString = await File.ReadAllTextAsync(_dataFilePath);
                
                if (string.IsNullOrWhiteSpace(jsonString))
                {
                    return new List<NewsletterSubscription>();
                }

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    WriteIndented = true
                };

                var subscriptions = JsonSerializer.Deserialize<List<NewsletterSubscription>>(jsonString, options);
                return subscriptions ?? new List<NewsletterSubscription>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deserializing newsletter subscriptions from {FilePath}", _dataFilePath);
                return new List<NewsletterSubscription>();
            }
        }

        /// <summary>
        /// Save subscriptions to JSON file
        /// </summary>
        /// <param name="subscriptions">List of subscriptions to save</param>
        private async Task SaveSubscriptionsAsync(List<NewsletterSubscription> subscriptions)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    WriteIndented = true
                };

                var jsonString = JsonSerializer.Serialize(subscriptions, options);
                await File.WriteAllTextAsync(_dataFilePath, jsonString);
                
                _logger.LogDebug("Saved {Count} newsletter subscriptions to {FilePath}", subscriptions.Count, _dataFilePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving newsletter subscriptions to {FilePath}", _dataFilePath);
                throw;
            }
        }

        #endregion
    }
}
