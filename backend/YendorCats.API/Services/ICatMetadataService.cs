using System.Collections.Generic;
using System.Threading.Tasks;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Service for comprehensive cat metadata and pedigree management
    /// </summary>
    public interface ICatMetadataService
    {
        /// <summary>
        /// Get comprehensive statistics about the cattery
        /// </summary>
        /// <returns>Cattery statistics</returns>
        Task<CatteryStatistics> GetCatteryStatisticsAsync();

    /// <summary>
    /// Get all cat profiles with metadata and photos
    /// </summary>
    /// <returns>List of cat profiles</returns>
    Task<List<CatMetadata>> GetAllCatProfilesAsync();

    /// <summary>
    /// Get a specific cat profile by ID
    /// </summary>
    /// <param name="catId">Cat identifier</param>
    /// <returns>Cat profile</returns>
    Task<CatMetadata?> GetCatProfileAsync(string catId);

    /// <summary>
    /// Create or update a cat profile
    /// </summary>
    /// <param name="profile">Cat profile data</param>
    /// <returns>Updated cat profile</returns>
    Task<CatMetadata> SaveCatProfileAsync(CatMetadata profile);

    /// <summary>
    /// Search cats by various criteria
    /// </summary>
    /// <param name="criteria">Search criteria</param>
    /// <returns>Matching cat profiles</returns>
    Task<List<CatMetadata>> SearchCatsAsync(CatSearchCriteria criteria);

        /// <summary>
        /// Get unlinked photos that don't have cat metadata
        /// </summary>
        /// <returns>List of unlinked photos</returns>
        Task<List<UnlinkedPhoto>> GetUnlinkedPhotosAsync();

        /// <summary>
        /// Link photos to a cat profile
        /// </summary>
        /// <param name="catId">Cat identifier</param>
        /// <param name="photoKeys">S3 keys of photos to link</param>
        /// <returns>Operation result</returns>
        Task<BulkOperationResult> LinkPhotosTosCatAsync(string catId, List<string> photoKeys);

        /// <summary>
        /// Update pedigree relationships for a cat
        /// </summary>
        /// <param name="catId">Cat identifier</param>
        /// <param name="fatherId">Father's cat ID</param>
        /// <param name="motherId">Mother's cat ID</param>
        /// <returns>Operation result</returns>
        Task<bool> UpdatePedigreeRelationshipsAsync(string catId, string? fatherId, string? motherId);

        /// <summary>
        /// Get family tree for a cat
        /// </summary>
        /// <param name="catId">Cat identifier</param>
        /// <param name="generations">Number of generations to retrieve</param>
        /// <returns>Family tree structure</returns>
        Task<FamilyTree> GetFamilyTreeAsync(string catId, int generations = 3);

        /// <summary>
        /// Process a litter with common metadata
        /// </summary>
        /// <param name="litterData">Litter processing data</param>
        /// <returns>Operation result</returns>
        Task<BulkOperationResult> ProcessLitterAsync(LitterData litterData);

        /// <summary>
        /// Apply metadata updates to multiple photos
        /// </summary>
        /// <param name="updates">Batch of metadata updates</param>
        /// <returns>Operation result</returns>
        Task<BulkOperationResult> BulkUpdatePhotosAsync(List<PhotoMetadataUpdate> updates);

        /// <summary>
        /// Validate pedigree relationships for data integrity
        /// </summary>
        /// <returns>Validation results</returns>
        Task<PedigreeValidationResult> ValidatePedigreeIntegrityAsync();

        /// <summary>
        /// Get bloodline information and statistics
        /// </summary>
        /// <returns>Bloodline data</returns>
        Task<List<BloodlineInfo>> GetBloodlineInfoAsync();
    }

    #region Data Models

    /// <summary>
    /// Comprehensive cattery statistics
    /// </summary>
    public class CatteryStatistics
    {
        public int TotalCats { get; set; }
        public int TotalPhotos { get; set; }
        public int UnlinkedPhotos { get; set; }
        public int AvailableKittens { get; set; }
        public int BreedingQueens { get; set; }
        public int Studs { get; set; }
        public int RetiredCats { get; set; }
        public int Bloodlines { get; set; }
        public DateTime LastUpdated { get; set; }
        public Dictionary<string, int> PhotosByType { get; set; } = new();
        public Dictionary<string, int> CatsByAge { get; set; } = new();
        public Dictionary<string, int> CatsByStatus { get; set; } = new();
    }

    /// <summary>
    /// Complete cat metadata with photos - distinct from Entity Framework CatProfile model
    /// </summary>
    public class CatMetadata
    {
        public string CatId { get; set; } = string.Empty;
        public string CatName { get; set; } = string.Empty;
        public string RegisteredName { get; set; } = string.Empty;
        public string RegistrationNumber { get; set; } = string.Empty;
        public string Breed { get; set; } = "Maine Coon";
        public string Gender { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public string Bloodline { get; set; } = string.Empty;
        public DateTime? BirthDate { get; set; }
        public string BreedingStatus { get; set; } = string.Empty;
        public string AvailabilityStatus { get; set; } = string.Empty;
        public string Personality { get; set; } = string.Empty;
        public string ChampionTitles { get; set; } = string.Empty;
        public string FatherId { get; set; } = string.Empty;
        public string MotherId { get; set; } = string.Empty;
        public List<CatPhoto> Photos { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public string UpdatedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// Cat photo with metadata
    /// </summary>
    public class CatPhoto
    {
        public string S3Key { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public string PhotoType { get; set; } = string.Empty;
        public string AgeAtPhoto { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime DateTaken { get; set; }
        public long Size { get; set; }
        public DateTime LastModified { get; set; }
        public Dictionary<string, string> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Search criteria for cats
    /// </summary>
    public class CatSearchCriteria
    {
        public string? CatName { get; set; }
        public string? Breed { get; set; }
        public string? Bloodline { get; set; }
        public string? BreedingStatus { get; set; }
        public string? AvailabilityStatus { get; set; }
        public string? Gender { get; set; }
        public string? Color { get; set; }
        public DateTime? BornAfter { get; set; }
        public DateTime? BornBefore { get; set; }
        public string? FatherId { get; set; }
        public string? MotherId { get; set; }
        public string? Prefix { get; set; }
    }

    /// <summary>
    /// Unlinked photo information
    /// </summary>
    public class UnlinkedPhoto
    {
        public string S3Key { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public long Size { get; set; }
        public DateTime LastModified { get; set; }
        public Dictionary<string, string> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Result of bulk operations
    /// </summary>
    public class BulkOperationResult
    {
        public bool Success { get; set; }
        public int TotalItems { get; set; }
        public int SuccessfulItems { get; set; }
        public int FailedItems { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public string Message { get; set; } = string.Empty;
        public DateTime CompletedAt { get; set; }
    }

    /// <summary>
    /// Family tree structure
    /// </summary>
    public class FamilyTree
    {
        public CatMetadata Cat { get; set; } = new();
        public FamilyTree? Father { get; set; }
        public FamilyTree? Mother { get; set; }
        public List<CatMetadata> Children { get; set; } = new();
        public int Generation { get; set; }
    }

    /// <summary>
    /// Litter processing data
    /// </summary>
    public class LitterData
    {
        public DateTime BirthDate { get; set; }
        public string MotherId { get; set; } = string.Empty;
        public string FatherId { get; set; } = string.Empty;
        public string Bloodline { get; set; } = string.Empty;
        public int KittenCount { get; set; }
        public List<string> PhotoKeys { get; set; } = new();
        public string LitterPrefix { get; set; } = string.Empty;
        public Dictionary<string, string> CommonMetadata { get; set; } = new();
    }

    /// <summary>
    /// Photo metadata update
    /// </summary>
    public class PhotoMetadataUpdate
    {
        public string S3Key { get; set; } = string.Empty;
        public Dictionary<string, string> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Pedigree validation result
    /// </summary>
    public class PedigreeValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public int CatsValidated { get; set; }
        public int RelationshipsValidated { get; set; }
        public DateTime ValidatedAt { get; set; }
    }

    /// <summary>
    /// Bloodline information
    /// </summary>
    public class BloodlineInfo
    {
        public string Name { get; set; } = string.Empty;
        public int CatCount { get; set; }
        public int PhotoCount { get; set; }
        public List<string> ChampionTitles { get; set; } = new();
        public DateTime EarliestBirthDate { get; set; }
        public DateTime LatestBirthDate { get; set; }
        public string FoundingCat { get; set; } = string.Empty;
    }

    #endregion
}
