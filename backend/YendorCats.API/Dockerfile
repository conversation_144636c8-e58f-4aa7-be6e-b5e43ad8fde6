# Stage 1: Build the application
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy the solution file and restore dependencies
COPY yendorcats.sln .
COPY backend/YendorCats.API/YendorCats.API.csproj backend/YendorCats.API/
RUN dotnet restore yendorcats.sln

# Copy only the backend application code (exclude frontend)
COPY backend/ backend/
WORKDIR /src/backend/YendorCats.API
RUN dotnet restore
# Set environment variable to disable frontend build targets
ENV CONTAINERIZED_BUILD=true
RUN dotnet publish -c Release -o /app/publish

# Stage 2: Create migrations and build the database
FROM build AS database-builder
WORKDIR /src/backend/YendorCats.API

# Install EF tools and create database
RUN dotnet tool install --global dotnet-ef && \
    export PATH="$PATH:$HOME/.dotnet/tools" && \
    mkdir -p /app/data && \
    export ConnectionStrings__SqliteConnection="Data Source=/app/data/yendorcats.db" && \
    (dotnet ef database update --no-build && chmod 644 /app/data/yendorcats.db) || \
    (echo "Database creation failed, creating empty file" && touch /app/data/yendorcats.db.empty)

# Stage 3: Create the runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Create data directory and set permissions
RUN mkdir -p /app/data && chmod 777 /app/data

# Copy the published application
COPY --from=build /app/publish .

# Copy the pre-built database (if it exists)
RUN mkdir -p /app/data
COPY --from=database-builder /app/data/ /app/data/

# Copy database scripts if they exist (using RUN to handle optional files)
RUN if [ -f /src/backend/YendorCats.API/Data/init-db.sh ]; then \
        cp /src/backend/YendorCats.API/Data/init-db.sh /app/; \
    else \
        echo "No init-db.sh found, skipping..."; \
    fi

# Add entrypoint script to handle database initialization
RUN echo '#!/bin/bash \n\
echo "Starting YendorCats API..." \n\
if [ ! -f /app/data/yendorcats.db ]; then \n\
  echo "No database found, will be created at runtime..." \n\
else \n\
  echo "Using existing database..." \n\
fi \n\
exec dotnet YendorCats.API.dll \n\
' > /app/entrypoint.sh && chmod +x /app/entrypoint.sh

# Set up volumes for data persistence
VOLUME ["/app/data"]

# Set environment variable to disable static file serving in containerized mode
ENV CONTAINERIZED_BUILD=true

# Expose port 80 (default for ASP.NET Core in containers)
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

ENTRYPOINT ["/app/entrypoint.sh"]
