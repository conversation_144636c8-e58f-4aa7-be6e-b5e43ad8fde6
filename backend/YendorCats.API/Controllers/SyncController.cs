using Microsoft.AspNetCore.Mvc;
using YendorCats.API.Services;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Controller for metadata synchronization operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class SyncController : ControllerBase
    {
        private readonly ILogger<SyncController> _logger;
        private readonly IMetadataSyncService _metadataSyncService;

        public SyncController(
            ILogger<SyncController> logger,
            IMetadataSyncService metadataSyncService)
        {
            _logger = logger;
            _metadataSyncService = metadataSyncService;
        }

        /// <summary>
        /// Trigger immediate metadata synchronization from B2 bucket
        /// </summary>
        /// <param name="category">Optional specific category to sync (studs, queens, kittens, gallery)</param>
        /// <returns>Sync result with statistics</returns>
        [HttpPost("metadata")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> TriggerMetadataSync([FromQuery] string? category = null)
        {
            try
            {
                _logger.LogInformation("Manual metadata sync triggered for category: {Category}", category ?? "all");

                // Validate category if provided
                if (!string.IsNullOrEmpty(category))
                {
                    var validCategories = new[] { "studs", "queens", "kittens", "gallery" };
                    if (!validCategories.Contains(category.ToLower()))
                    {
                        return BadRequest(new { message = "Invalid category. Valid categories: studs, queens, kittens, gallery" });
                    }
                }

                var result = await _metadataSyncService.TriggerImmediateSyncAsync(category);

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = result.Success,
                        message = result.Message,
                        syncTime = result.SyncTime,
                        duration = result.Duration.TotalMilliseconds,
                        statistics = new
                        {
                            categoriesProcessed = result.CategoriesProcessed,
                            imagesProcessed = result.ImagesProcessed,
                            imagesAdded = result.ImagesAdded,
                            imagesUpdated = result.ImagesUpdated
                        }
                    });
                }
                else
                {
                    return StatusCode(500, new
                    {
                        success = result.Success,
                        message = result.Message,
                        errors = result.Errors
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error triggering metadata sync");
                return StatusCode(500, new { message = "An error occurred while triggering metadata sync" });
            }
        }

        /// <summary>
        /// Get the status of the last metadata synchronization
        /// </summary>
        /// <returns>Last sync status information</returns>
        [HttpGet("metadata/status")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetMetadataSyncStatus()
        {
            try
            {
                var status = await _metadataSyncService.GetLastSyncStatusAsync();

                return Ok(new
                {
                    lastSyncTime = status.LastSyncTime,
                    isRecentSync = status.IsRecentSync,
                    nextScheduledSync = status.NextScheduledSync,
                    databaseImageCount = status.DatabaseImageCount,
                    syncIntervalMinutes = 30
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving metadata sync status");
                return StatusCode(500, new { message = "An error occurred while retrieving sync status" });
            }
        }

        /// <summary>
        /// Perform automatic metadata sync (called periodically)
        /// </summary>
        /// <returns>Sync result if sync was needed</returns>
        [HttpPost("metadata/auto")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status304NotModified)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> AutoMetadataSync()
        {
            try
            {
                _logger.LogDebug("Auto metadata sync check");

                var result = await _metadataSyncService.SyncMetadataAsync();

                if (result.Message.Contains("not needed"))
                {
                    return StatusCode(304, new
                    {
                        success = true,
                        message = result.Message,
                        lastSyncTime = result.SyncTime
                    });
                }

                return Ok(new
                {
                    success = result.Success,
                    message = result.Message,
                    syncTime = result.SyncTime,
                    duration = result.Duration.TotalMilliseconds,
                    statistics = new
                    {
                        categoriesProcessed = result.CategoriesProcessed,
                        imagesProcessed = result.ImagesProcessed,
                        imagesAdded = result.ImagesAdded,
                        imagesUpdated = result.ImagesUpdated
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in auto metadata sync");
                return StatusCode(500, new { message = "An error occurred during automatic sync" });
            }
        }

        /// <summary>
        /// Force re-sync metadata, overwriting existing fallback data with S3 metadata
        /// Specifically designed to fix Queen gallery "Beautiful Queen" issue
        /// </summary>
        /// <param name="category">Optional specific category to force sync (studs, queens, kittens, gallery)</param>
        /// <param name="overwriteFallback">Whether to overwrite existing fallback data (default: true)</param>
        /// <returns>Force sync result with detailed statistics</returns>
        [HttpPost("metadata/force-resync")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ForceResyncMetadata(
            [FromQuery] string? category = null,
            [FromQuery] bool overwriteFallback = true)
        {
            try
            {
                _logger.LogInformation("Force metadata re-sync triggered for category: {Category}, overwrite fallback: {OverwriteFallback}",
                    category ?? "all", overwriteFallback);

                // Validate category if provided
                if (!string.IsNullOrEmpty(category))
                {
                    var validCategories = new[] { "studs", "queens", "kittens", "gallery" };
                    if (!validCategories.Contains(category.ToLower()))
                    {
                        return BadRequest(new {
                            success = false,
                            message = "Invalid category. Valid categories: studs, queens, kittens, gallery",
                            errorCode = "INVALID_CATEGORY"
                        });
                    }
                }

                var result = await _metadataSyncService.ForceResyncMetadataAsync(category, overwriteFallback);

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = result.Success,
                        message = result.Message,
                        syncTime = result.SyncTime,
                        duration = result.Duration.TotalMilliseconds,
                        statistics = new
                        {
                            categoriesProcessed = result.CategoriesProcessed,
                            imagesProcessed = result.ImagesProcessed,
                            imagesAdded = result.ImagesAdded,
                            imagesUpdated = result.ImagesUpdated,
                            fallbackDataOverwritten = result.ImagesUpdated // Updated images are fallback data that was overwritten
                        },
                        metadata = new
                        {
                            category = category ?? "all",
                            overwriteFallback = overwriteFallback,
                            purpose = "Fix Queen gallery 'Beautiful Queen' fallback data with actual S3 metadata"
                        }
                    });
                }
                else
                {
                    return StatusCode(500, new
                    {
                        success = result.Success,
                        message = result.Message,
                        errors = result.Errors,
                        partialResults = new
                        {
                            categoriesProcessed = result.CategoriesProcessed,
                            imagesProcessed = result.ImagesProcessed,
                            imagesUpdated = result.ImagesUpdated
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error triggering force metadata re-sync");
                return StatusCode(500, new {
                    success = false,
                    message = "An error occurred while triggering force metadata re-sync",
                    errorCode = "FORCE_RESYNC_ERROR"
                });
            }
        }
    }
}
