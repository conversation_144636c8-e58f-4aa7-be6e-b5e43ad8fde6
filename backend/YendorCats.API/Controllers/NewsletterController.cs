using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using YendorCats.API.Models;
using YendorCats.API.Services;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Controller for newsletter subscription management
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class NewsletterController : ControllerBase
    {
        private readonly ILogger<NewsletterController> _logger;
        private readonly INewsletterService _newsletterService;

        /// <summary>
        /// Constructor for newsletter controller
        /// </summary>
        /// <param name="logger">Logger instance</param>
        /// <param name="newsletterService">Newsletter service</param>
        public NewsletterController(ILogger<NewsletterController> logger, INewsletterService newsletterService)
        {
            _logger = logger;
            _newsletterService = newsletterService;
        }

        /// <summary>
        /// Subscribe to newsletter (public endpoint)
        /// </summary>
        /// <param name="request">Newsletter subscription request</param>
        /// <returns>Subscription result</returns>
        [HttpPost("subscribe")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Subscribe([FromBody] NewsletterSubscriptionRequest request)
        {
            _logger.LogInformation("Newsletter subscription request from {Email}", request.Email);

            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { 
                        success = false, 
                        message = "Invalid subscription data", 
                        errors = ModelState 
                    });
                }

                // Check if email is already subscribed
                var isAlreadySubscribed = await _newsletterService.IsEmailSubscribedAsync(request.Email);
                if (isAlreadySubscribed)
                {
                    _logger.LogWarning("Duplicate subscription attempt for {Email}", request.Email);
                    return Conflict(new { 
                        success = false, 
                        message = "This email address is already subscribed to our newsletter." 
                    });
                }

                // Get client IP address
                var ipAddress = GetClientIpAddress();

                // Create subscription
                var subscription = new NewsletterSubscription
                {
                    Name = request.Name,
                    Email = request.Email,
                    Interests = request.Interests,
                    IpAddress = ipAddress
                };

                var success = await _newsletterService.AddSubscriptionAsync(subscription);

                if (success)
                {
                    _logger.LogInformation("Successfully added newsletter subscription for {Email}", request.Email);
                    return Ok(new { 
                        success = true, 
                        message = "Thank you for subscribing to our newsletter! You'll be notified about kitten availability and cattery news.",
                        subscriptionId = subscription.Id
                    });
                }
                else
                {
                    _logger.LogError("Failed to add newsletter subscription for {Email}", request.Email);
                    return StatusCode(500, new { 
                        success = false, 
                        message = "Unable to process your subscription. Please try again later." 
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing newsletter subscription for {Email}", request.Email);
                return StatusCode(500, new { 
                    success = false, 
                    message = "An error occurred while processing your subscription." 
                });
            }
        }

        /// <summary>
        /// Get all newsletter subscriptions (admin only)
        /// </summary>
        /// <returns>List of all subscriptions</returns>
        [HttpGet("admin/subscriptions")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetAllSubscriptions()
        {
            var username = User.Identity?.Name ?? "Unknown Admin";
            _logger.LogInformation("Admin {Username} requesting all newsletter subscriptions", username);

            try
            {
                var subscriptions = await _newsletterService.GetAllSubscriptionsAsync();
                
                // Group by status for summary
                var statusSummary = subscriptions
                    .GroupBy(s => s.Status)
                    .ToDictionary(g => g.Key, g => g.Count());

                // Interest summary
                var interestSummary = subscriptions
                    .Where(s => !string.IsNullOrEmpty(s.Interests))
                    .GroupBy(s => s.Interests!)
                    .ToDictionary(g => g.Key, g => g.Count());

                return Ok(new {
                    success = true,
                    totalSubscriptions = subscriptions.Count,
                    statusSummary = statusSummary,
                    interestSummary = interestSummary,
                    subscriptions = subscriptions.Select(s => new {
                        id = s.Id,
                        name = s.Name,
                        email = s.Email,
                        interests = s.Interests,
                        subscribedAt = s.SubscribedAt,
                        status = s.Status,
                        notes = s.Notes,
                        lastUpdated = s.LastUpdated,
                        lastUpdatedBy = s.LastUpdatedBy,
                        ipAddress = s.IpAddress
                    }).ToList(),
                    retrievedBy = username,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving newsletter subscriptions");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error retrieving newsletter subscriptions" 
                });
            }
        }

        /// <summary>
        /// Get subscriptions by status (admin only)
        /// </summary>
        /// <param name="status">Status to filter by</param>
        /// <returns>Filtered subscriptions</returns>
        [HttpGet("admin/subscriptions/status/{status}")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetSubscriptionsByStatus(string status)
        {
            var username = User.Identity?.Name ?? "Unknown Admin";
            _logger.LogInformation("Admin {Username} requesting newsletter subscriptions with status {Status}", 
                username, status);

            try
            {
                var subscriptions = await _newsletterService.GetSubscriptionsByStatusAsync(status);

                return Ok(new {
                    success = true,
                    status = status,
                    count = subscriptions.Count,
                    subscriptions = subscriptions.Select(s => new {
                        id = s.Id,
                        name = s.Name,
                        email = s.Email,
                        interests = s.Interests,
                        subscribedAt = s.SubscribedAt,
                        status = s.Status,
                        notes = s.Notes,
                        lastUpdated = s.LastUpdated,
                        lastUpdatedBy = s.LastUpdatedBy
                    }).ToList(),
                    retrievedBy = username,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving newsletter subscriptions by status {Status}", status);
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error retrieving newsletter subscriptions" 
                });
            }
        }

        /// <summary>
        /// Update newsletter subscription status (admin only)
        /// </summary>
        /// <param name="id">Subscription ID</param>
        /// <param name="request">Update request</param>
        /// <returns>Update result</returns>
        [HttpPut("admin/subscriptions/{id}/status")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateSubscriptionStatus(string id, [FromBody] UpdateNewsletterStatusRequest request)
        {
            var username = User.Identity?.Name ?? "Unknown Admin";
            _logger.LogInformation("Admin {Username} updating newsletter subscription {Id} status to {Status}", 
                username, id, request.Status);

            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { 
                        success = false, 
                        message = "Invalid update data", 
                        errors = ModelState 
                    });
                }

                // Check if subscription exists
                var subscription = await _newsletterService.GetSubscriptionByIdAsync(id);
                if (subscription == null)
                {
                    return NotFound(new { 
                        success = false, 
                        message = "Newsletter subscription not found" 
                    });
                }

                var success = await _newsletterService.UpdateSubscriptionStatusAsync(
                    id, 
                    request.Status, 
                    request.Notes, 
                    username
                );

                if (success)
                {
                    _logger.LogInformation("Successfully updated newsletter subscription {Id}", id);
                    return Ok(new { 
                        success = true, 
                        message = "Subscription status updated successfully",
                        updatedBy = username,
                        updatedAt = DateTime.UtcNow
                    });
                }
                else
                {
                    _logger.LogError("Failed to update newsletter subscription {Id}", id);
                    return StatusCode(500, new { 
                        success = false, 
                        message = "Unable to update subscription status" 
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating newsletter subscription {Id}", id);
                return StatusCode(500, new { 
                    success = false, 
                    message = "An error occurred while updating the subscription" 
                });
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Get the client IP address from the request
        /// </summary>
        /// <returns>Client IP address</returns>
        private string GetClientIpAddress()
        {
            try
            {
                // Check for forwarded headers first (for load balancers/proxies)
                var forwardedFor = Request.Headers["X-Forwarded-For"].FirstOrDefault();
                if (!string.IsNullOrEmpty(forwardedFor))
                {
                    return forwardedFor.Split(',')[0].Trim();
                }

                var realIp = Request.Headers["X-Real-IP"].FirstOrDefault();
                if (!string.IsNullOrEmpty(realIp))
                {
                    return realIp;
                }

                // Fall back to connection remote IP
                return HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        #endregion
    }
}
