using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using YendorCats.API.Attributes;
using YendorCats.API.Services;
using System.ComponentModel.DataAnnotations;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Controller for comprehensive cat management and metadata operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [AdminAuthorize("SuperAdmin", "Admin", "Editor")]
    public class CatManagementController : ControllerBase
    {
        private readonly ICatMetadataService _catMetadataService;
        private readonly ILogger<CatManagementController> _logger;

        public CatManagementController(
            ICatMetadataService catMetadataService,
            ILogger<CatManagementController> logger)
        {
            _catMetadataService = catMetadataService;
            _logger = logger;
        }

        /// <summary>
        /// Get comprehensive cattery statistics
        /// </summary>
        /// <returns>Cattery statistics</returns>
        [HttpGet("statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetCatteryStatistics()
        {
            try
            {
                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} requesting cattery statistics", adminUser?.Username);

                var statistics = await _catMetadataService.GetCatteryStatisticsAsync();

                return Ok(new {
                    success = true,
                    statistics = statistics,
                    retrievedBy = adminUser?.Username,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cattery statistics");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error retrieving cattery statistics" 
                });
            }
        }

        /// <summary>
        /// Get all cat profiles with metadata and photos
        /// </summary>
        /// <returns>List of cat profiles</returns>
        [HttpGet("profiles")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetAllCatProfiles()
        {
            try
            {
                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} requesting all cat profiles", adminUser?.Username);

                var profiles = await _catMetadataService.GetAllCatProfilesAsync();

                return Ok(new {
                    success = true,
                    totalCount = profiles.Count,
                    profiles = profiles,
                    retrievedBy = adminUser?.Username,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cat profiles");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error retrieving cat profiles" 
                });
            }
        }

        /// <summary>
        /// Get a specific cat profile by ID
        /// </summary>
        /// <param name="catId">Cat identifier</param>
        /// <returns>Cat profile</returns>
        [HttpGet("profiles/{catId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetCatProfile(string catId)
        {
            try
            {
                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} requesting cat profile: {CatId}", adminUser?.Username, catId);

                var profile = await _catMetadataService.GetCatProfileAsync(catId);

                if (profile == null)
                {
                    return NotFound(new { 
                        success = false, 
                        message = $"Cat profile not found: {catId}" 
                    });
                }

                return Ok(new {
                    success = true,
                    profile = profile,
                    retrievedBy = adminUser?.Username,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cat profile: {CatId}", catId);
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error retrieving cat profile" 
                });
            }
        }

        /// <summary>
        /// Create or update a cat profile
        /// </summary>
        /// <param name="profile">Cat profile data</param>
        /// <returns>Updated cat profile</returns>
        [HttpPost("profiles")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SaveCatProfile([FromBody] CatMetadata profile)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} saving cat profile: {CatId} - {CatName}", 
                    adminUser?.Username, profile.CatId, profile.CatName);

                profile.UpdatedBy = adminUser?.Username ?? "system";
                
                if (string.IsNullOrEmpty(profile.CreatedBy))
                {
                    profile.CreatedBy = adminUser?.Username ?? "system";
                    profile.CreatedAt = DateTime.UtcNow;
                }

                var savedProfile = await _catMetadataService.SaveCatProfileAsync(profile);

                return Ok(new {
                    success = true,
                    message = $"Cat profile saved successfully: {savedProfile.CatName}",
                    profile = savedProfile,
                    savedBy = adminUser?.Username,
                    savedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving cat profile: {CatId}", profile.CatId);
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error saving cat profile" 
                });
            }
        }

        /// <summary>
        /// Search cats by various criteria
        /// </summary>
        /// <param name="criteria">Search criteria</param>
        /// <returns>Matching cat profiles</returns>
        [HttpPost("search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SearchCats([FromBody] CatSearchCriteria criteria)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} searching cats", adminUser?.Username);

                var results = await _catMetadataService.SearchCatsAsync(criteria);

                return Ok(new {
                    success = true,
                    searchCriteria = criteria,
                    resultCount = results.Count,
                    results = results,
                    searchedBy = adminUser?.Username,
                    searchedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching cats");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error searching cats" 
                });
            }
        }

        /// <summary>
        /// Get unlinked photos that don't have cat metadata
        /// </summary>
        /// <returns>List of unlinked photos</returns>
        [HttpGet("photos/unlinked")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetUnlinkedPhotos()
        {
            try
            {
                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} requesting unlinked photos", adminUser?.Username);

                var unlinkedPhotos = await _catMetadataService.GetUnlinkedPhotosAsync();

                return Ok(new {
                    success = true,
                    count = unlinkedPhotos.Count,
                    photos = unlinkedPhotos,
                    retrievedBy = adminUser?.Username,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving unlinked photos");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error retrieving unlinked photos" 
                });
            }
        }

        /// <summary>
        /// Link photos to a cat profile
        /// </summary>
        /// <param name="request">Photo linking request</param>
        /// <returns>Operation result</returns>
        [HttpPost("photos/link")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> LinkPhotosTosCat([FromBody] LinkPhotosRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} linking {Count} photos to cat: {CatId}", 
                    adminUser?.Username, request.PhotoKeys.Count, request.CatId);

                var result = await _catMetadataService.LinkPhotosTosCatAsync(request.CatId, request.PhotoKeys);

                return Ok(new {
                    success = result.Success,
                    result = result,
                    linkedBy = adminUser?.Username,
                    linkedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error linking photos to cat");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error linking photos to cat" 
                });
            }
        }

        /// <summary>
        /// Update pedigree relationships for a cat
        /// </summary>
        /// <param name="request">Pedigree update request</param>
        /// <returns>Operation result</returns>
        [HttpPost("pedigree/update")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdatePedigreeRelationships([FromBody] UpdatePedigreeRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} updating pedigree relationships for cat: {CatId}", 
                    adminUser?.Username, request.CatId);

                var success = await _catMetadataService.UpdatePedigreeRelationshipsAsync(
                    request.CatId, request.FatherId, request.MotherId);

                return Ok(new {
                    success = success,
                    message = success ? "Pedigree relationships updated successfully" : "Failed to update pedigree relationships",
                    catId = request.CatId,
                    fatherId = request.FatherId,
                    motherId = request.MotherId,
                    updatedBy = adminUser?.Username,
                    updatedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating pedigree relationships");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error updating pedigree relationships" 
                });
            }
        }

        /// <summary>
        /// Get family tree for a cat
        /// </summary>
        /// <param name="catId">Cat identifier</param>
        /// <param name="generations">Number of generations to retrieve</param>
        /// <returns>Family tree structure</returns>
        [HttpGet("pedigree/{catId}/family-tree")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetFamilyTree(string catId, [FromQuery] int generations = 3)
        {
            try
            {
                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} requesting family tree for cat: {CatId}, generations: {Generations}", 
                    adminUser?.Username, catId, generations);

                var familyTree = await _catMetadataService.GetFamilyTreeAsync(catId, Math.Max(1, Math.Min(generations, 5)));

                return Ok(new {
                    success = true,
                    familyTree = familyTree,
                    generationsRequested = generations,
                    retrievedBy = adminUser?.Username,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { 
                    success = false, 
                    message = ex.Message 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving family tree for cat: {CatId}", catId);
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error retrieving family tree" 
                });
            }
        }

        /// <summary>
        /// Process a litter with common metadata
        /// </summary>
        /// <param name="litterData">Litter processing data</param>
        /// <returns>Operation result</returns>
        [HttpPost("litter/process")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ProcessLitter([FromBody] LitterData litterData)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} processing litter with {Count} photos", 
                    adminUser?.Username, litterData.PhotoKeys.Count);

                var result = await _catMetadataService.ProcessLitterAsync(litterData);

                return Ok(new {
                    success = result.Success,
                    result = result,
                    processedBy = adminUser?.Username,
                    processedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing litter");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error processing litter" 
                });
            }
        }

        /// <summary>
        /// Apply metadata updates to multiple photos
        /// </summary>
        /// <param name="updates">Batch of metadata updates</param>
        /// <returns>Operation result</returns>
        [HttpPost("photos/bulk-update")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> BulkUpdatePhotos([FromBody] List<PhotoMetadataUpdate> updates)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} performing bulk update on {Count} photos", 
                    adminUser?.Username, updates.Count);

                var result = await _catMetadataService.BulkUpdatePhotosAsync(updates);

                return Ok(new {
                    success = result.Success,
                    result = result,
                    updatedBy = adminUser?.Username,
                    updatedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing bulk photo update");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error performing bulk photo update" 
                });
            }
        }

        /// <summary>
        /// Validate pedigree relationships for data integrity
        /// </summary>
        /// <returns>Validation results</returns>
        [HttpPost("pedigree/validate")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> ValidatePedigreeIntegrity()
        {
            try
            {
                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} requesting pedigree validation", adminUser?.Username);

                var result = await _catMetadataService.ValidatePedigreeIntegrityAsync();

                return Ok(new {
                    success = true,
                    validation = result,
                    validatedBy = adminUser?.Username,
                    validatedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating pedigree integrity");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error validating pedigree integrity" 
                });
            }
        }

        /// <summary>
        /// Get bloodline information and statistics
        /// </summary>
        /// <returns>Bloodline data</returns>
        [HttpGet("bloodlines")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetBloodlineInfo()
        {
            try
            {
                var adminUser = HttpContext.GetAdminUser();
                _logger.LogInformation("Admin {Username} requesting bloodline information", adminUser?.Username);

                var bloodlines = await _catMetadataService.GetBloodlineInfoAsync();

                return Ok(new {
                    success = true,
                    totalBloodlines = bloodlines.Count,
                    bloodlines = bloodlines,
                    retrievedBy = adminUser?.Username,
                    retrievedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving bloodline information");
                return StatusCode(500, new { 
                    success = false, 
                    message = "Error retrieving bloodline information" 
                });
            }
        }
    }

    #region Request Models

    /// <summary>
    /// Request model for linking photos to a cat
    /// </summary>
    public class LinkPhotosRequest
    {
        /// <summary>
        /// Cat identifier
        /// </summary>
        [Required]
        public string CatId { get; set; } = string.Empty;

        /// <summary>
        /// S3 keys of photos to link
        /// </summary>
        [Required]
        public List<string> PhotoKeys { get; set; } = new();
    }

    /// <summary>
    /// Request model for updating pedigree relationships
    /// </summary>
    public class UpdatePedigreeRequest
    {
        /// <summary>
        /// Cat identifier
        /// </summary>
        [Required]
        public string CatId { get; set; } = string.Empty;

        /// <summary>
        /// Father's cat ID
        /// </summary>
        public string? FatherId { get; set; }

        /// <summary>
        /// Mother's cat ID
        /// </summary>
        public string? MotherId { get; set; }
    }

    #endregion
}
