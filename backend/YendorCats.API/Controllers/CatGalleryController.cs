using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using YendorCats.API.Models;
using YendorCats.API.Services;
using YendorCats.API.Data;
using Microsoft.EntityFrameworkCore;
using YendorCats.API.Attributes;

namespace YendorCats.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [AdminAuthorize("SuperAdmin", "Admin", "Editor")]  // ✅ SECURE: Require admin authentication
    public class CatGalleryController : ControllerBase
    {
        private readonly ILogger<CatGalleryController> _logger;
        private readonly IS3StorageService _s3StorageService;
        private readonly AppDbContext _context;
        private readonly string[] _validCategories = { "studs", "queens", "kittens", "gallery" };

        public CatGalleryController(
            ILogger<CatGalleryController> logger,
            IS3StorageService s3StorageService,
            AppDbContext context)
        {
            _logger = logger;
            _s3StorageService = s3StorageService;
            _context = context;
        }

        [HttpGet("category/{category}")]
        public async Task<ActionResult<IEnumerable<CatGalleryImage>>> GetCategoryImages(
            string category,
            [FromQuery] string orderBy = "date",
            [FromQuery] bool descending = true)
        {
            if (!_validCategories.Contains(category.ToLower()))
            {
                return BadRequest($"Invalid category. Valid categories are: {string.Join(", ", _validCategories)}");
            }

            try
            {
                var catImages = await ScanS3ForImagesAsync(category);

                IOrderedEnumerable<CatGalleryImage> orderedImages;
                switch (orderBy.ToLower())
                {
                    case "name":
                        orderedImages = descending
                            ? catImages.OrderByDescending(img => img.CatName)
                            : catImages.OrderBy(img => img.CatName);
                        break;
                    case "age":
                        orderedImages = descending
                            ? catImages.OrderByDescending(img => img.Age)
                            : catImages.OrderBy(img => img.Age);
                        break;
                    default:
                        orderedImages = descending
                            ? catImages.OrderByDescending(img => img.DateTaken).ThenBy(img => img.OrderNumber)
                            : catImages.OrderBy(img => img.DateTaken).ThenBy(img => img.OrderNumber);
                        break;
                }

                return orderedImages.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cat gallery images for category {Category}", category);
                return StatusCode(500, "An error occurred while retrieving cat gallery images");
            }
        }

        private async Task<List<CatGalleryImage>> ScanS3ForImagesAsync(string category)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = new List<CatGalleryImage>();

            try
            {
                // Get database images for reference
                var dbImages = await _context.CatGalleryImages
                    .Where(img => img.Category.ToLower() == category.ToLower())
                    .ToListAsync();

                _logger.LogInformation("Found {Count} images in database for category {Category}", dbImages.Count, category);

                // Create a lookup for database images by S3 key for efficient merging
                var dbImageLookup = dbImages
                    .Where(img => !string.IsNullOrEmpty(img.ImageUrl))
                    .ToDictionary(img => ExtractS3KeyFromUrl(img.ImageUrl), img => img);

                // Scan S3 for all images (prioritizing S3 metadata)
                var prefix = $"{category}/";
                var s3Files = await _s3StorageService.ListFilesAsync(prefix);

                if (!s3Files.Any())
                {
                    _logger.LogInformation("No S3 files found for category {Category}, returning database images only", category);
                    result.AddRange(dbImages);
                    return result;
                }

                // Process all S3 files and merge with database data (S3 metadata takes priority)
                var s3FilesToProcess = s3Files
                    .Where(s3Object => IsImageFile(s3Object.Key))
                    .ToList();

                _logger.LogInformation("Processing {Count} S3 files for category {Category} (prioritizing S3 metadata)", s3FilesToProcess.Count, category);

                // Process S3 files in parallel batches for better performance
                var s3Images = await ProcessS3FilesInBatchesWithDbMerge(s3FilesToProcess, category, dbImageLookup);
                result.AddRange(s3Images);

                // Add any database-only images that don't have S3 counterparts
                var s3Keys = s3FilesToProcess.Select(s3 => s3.Key).ToHashSet();
                var dbOnlyImages = dbImages.Where(dbImg =>
                {
                    var s3Key = ExtractS3KeyFromUrl(dbImg.ImageUrl);
                    return !s3Keys.Contains(s3Key);
                }).ToList();

                if (dbOnlyImages.Any())
                {
                    _logger.LogInformation("Adding {Count} database-only images for category {Category}", dbOnlyImages.Count, category);
                    result.AddRange(dbOnlyImages);
                }

                stopwatch.Stop();
                _logger.LogInformation("Total images found for category {Category}: {Count} (S3: {S3Count}, DB-only: {DbOnlyCount}) in {ElapsedMs}ms",
                    category, result.Count, s3Images.Count, dbOnlyImages.Count, stopwatch.ElapsedMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error scanning S3 for images in category {Category}", category);
                throw;
            }
        }

        private async Task<List<CatGalleryImage>> ProcessS3FilesInBatches(List<Amazon.S3.Model.S3Object> s3Files, string category)
        {
            const int batchSize = 10; // Process 10 files at a time to avoid overwhelming S3
            var result = new List<CatGalleryImage>();
            var totalBatches = (int)Math.Ceiling((double)s3Files.Count / batchSize);

            for (int i = 0; i < totalBatches; i++)
            {
                var batch = s3Files.Skip(i * batchSize).Take(batchSize).ToList();
                var batchTasks = batch.Select(s3Object => ProcessSingleS3File(s3Object, category)).ToArray();
                
                try
                {
                    var batchResults = await Task.WhenAll(batchTasks);
                    var validResults = batchResults.Where(r => r != null).ToList();
                    result.AddRange(validResults);
                    
                    _logger.LogDebug("Processed batch {BatchNumber}/{TotalBatches} for category {Category}: {SuccessCount}/{BatchSize} successful",
                        i + 1, totalBatches, category, validResults.Count, batch.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error processing batch {BatchNumber} for category {Category}", i + 1, category);
                    
                    // Process failed batch items individually to salvage what we can
                    foreach (var s3Object in batch)
                    {
                        try
                        {
                            var catImage = await ProcessSingleS3File(s3Object, category);
                            if (catImage != null)
                            {
                                result.Add(catImage);
                            }
                        }
                        catch (Exception individualEx)
                        {
                            _logger.LogWarning(individualEx, "Failed to process individual S3 file: {S3Key}", s3Object.Key);
                        }
                    }
                }

                // Small delay between batches to avoid rate limiting
                if (i < totalBatches - 1)
                {
                    await Task.Delay(100);
                }
            }

            return result;
        }

        private async Task<List<CatGalleryImage>> ProcessS3FilesInBatchesWithDbMerge(
            List<Amazon.S3.Model.S3Object> s3Files,
            string category,
            Dictionary<string, CatGalleryImage> dbImageLookup)
        {
            const int batchSize = 10; // Process 10 files at a time to avoid overwhelming S3
            var result = new List<CatGalleryImage>();
            var totalBatches = (int)Math.Ceiling((double)s3Files.Count / batchSize);

            for (int i = 0; i < totalBatches; i++)
            {
                var batch = s3Files.Skip(i * batchSize).Take(batchSize).ToList();
                var batchTasks = batch.Select(s3Object => ProcessSingleS3FileWithDbMerge(s3Object, category, dbImageLookup)).ToArray();

                try
                {
                    var batchResults = await Task.WhenAll(batchTasks);
                    var validResults = batchResults.Where(r => r != null).ToList();
                    result.AddRange(validResults);

                    _logger.LogDebug("Processed batch {BatchNumber}/{TotalBatches} for category {Category}: {SuccessCount}/{BatchSize} successful (with DB merge)",
                        i + 1, totalBatches, category, validResults.Count, batch.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error processing batch {BatchNumber} for category {Category} (with DB merge)", i + 1, category);

                    // Process failed batch items individually to salvage what we can
                    foreach (var s3Object in batch)
                    {
                        try
                        {
                            var catImage = await ProcessSingleS3FileWithDbMerge(s3Object, category, dbImageLookup);
                            if (catImage != null)
                            {
                                result.Add(catImage);
                            }
                        }
                        catch (Exception individualEx)
                        {
                            _logger.LogWarning(individualEx, "Failed to process individual S3 file with DB merge: {S3Key}", s3Object.Key);
                        }
                    }
                }

                // Small delay between batches to avoid rate limiting
                if (i < totalBatches - 1)
                {
                    await Task.Delay(100);
                }
            }

            return result;
        }

        private async Task<CatGalleryImage?> ProcessSingleS3File(Amazon.S3.Model.S3Object s3Object, string category)
        {
            try
            {
                var imageUrl = $"https://f004.backblazeb2.com/file/yendor/{s3Object.Key}";
                var metadata = await _s3StorageService.GetObjectMetadataAsync(s3Object.Key);

                // Log metadata for debugging
                if (metadata.Count > 0)
                {
                    _logger.LogDebug("S3 metadata for {S3Key}: {MetadataKeys}", s3Object.Key,
                        string.Join(", ", metadata.Select(kvp => $"{kvp.Key}={kvp.Value}")));
                }
                else
                {
                    _logger.LogWarning("No metadata found for S3 file: {S3Key}", s3Object.Key);
                }

                var catImage = CatGalleryImage.FromS3Metadata(metadata, imageUrl, category, null);

                // Log what was extracted from metadata
                _logger.LogDebug("Extracted from S3 metadata for {S3Key}: CatName='{CatName}', Description='{Description}', Age='{Age}', Gender='{Gender}', Breed='{Breed}'",
                    s3Object.Key, catImage.CatName, catImage.Description, catImage.AgeAtPhoto, catImage.Gender, catImage.Breed);

                // Try to extract cat name from folder structure as fallback
                if (string.IsNullOrEmpty(catImage.CatName))
                {
                    var fallbackName = ExtractCatNameFromPath(s3Object.Key);
                    catImage.CatName = fallbackName;
                    _logger.LogDebug("Using fallback cat name for {S3Key}: {FallbackName}", s3Object.Key, fallbackName);
                }

                return catImage;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to process S3 file: {S3Key}", s3Object.Key);
                return null;
            }
        }

        private async Task<CatGalleryImage?> ProcessSingleS3FileWithDbMerge(
            Amazon.S3.Model.S3Object s3Object,
            string category,
            Dictionary<string, CatGalleryImage> dbImageLookup)
        {
            try
            {
                var imageUrl = $"https://f004.backblazeb2.com/file/yendor/{s3Object.Key}";
                var metadata = await _s3StorageService.GetObjectMetadataAsync(s3Object.Key);

                // Log metadata for debugging
                if (metadata.Count > 0)
                {
                    _logger.LogDebug("S3 metadata for {S3Key}: {MetadataKeys}", s3Object.Key,
                        string.Join(", ", metadata.Select(kvp => $"{kvp.Key}={kvp.Value}")));
                }
                else
                {
                    _logger.LogWarning("No metadata found for S3 file: {S3Key}", s3Object.Key);
                }

                // Create image from S3 metadata (prioritized)
                var catImage = CatGalleryImage.FromS3Metadata(metadata, imageUrl, category, null);

                // Check if there's a corresponding database entry
                if (dbImageLookup.TryGetValue(s3Object.Key, out var dbImage))
                {
                    _logger.LogDebug("Merging S3 metadata with database data for {S3Key}", s3Object.Key);

                    // Merge data: S3 metadata takes priority, but use database values as fallback
                    // Only use database values if S3 metadata is empty/missing
                    if (string.IsNullOrEmpty(catImage.CatName) && !string.IsNullOrEmpty(dbImage.CatName))
                        catImage.CatName = dbImage.CatName;

                    if (string.IsNullOrEmpty(catImage.Description) && !string.IsNullOrEmpty(dbImage.Description))
                        catImage.Description = dbImage.Description;

                    if (string.IsNullOrEmpty(catImage.AgeAtPhoto) && !string.IsNullOrEmpty(dbImage.AgeAtPhoto))
                        catImage.AgeAtPhoto = dbImage.AgeAtPhoto;

                    if (string.IsNullOrEmpty(catImage.Gender) && !string.IsNullOrEmpty(dbImage.Gender))
                        catImage.Gender = dbImage.Gender;

                    if (string.IsNullOrEmpty(catImage.Bloodline) && !string.IsNullOrEmpty(dbImage.Bloodline))
                        catImage.Bloodline = dbImage.Bloodline;

                    if (string.IsNullOrEmpty(catImage.Tags) && !string.IsNullOrEmpty(dbImage.Tags))
                        catImage.Tags = dbImage.Tags;

                    // Use database ID and other system fields
                    catImage.Id = dbImage.Id;
                    catImage.CatId = dbImage.CatId;
                    catImage.CreatedAt = dbImage.CreatedAt;
                    catImage.UpdatedAt = dbImage.UpdatedAt;
                    catImage.ViewCount = dbImage.ViewCount;
                    catImage.LikeCount = dbImage.LikeCount;
                    catImage.DownloadCount = dbImage.DownloadCount;
                    catImage.DisplayOrder = dbImage.DisplayOrder;
                    catImage.SortOrder = dbImage.SortOrder;

                    // Use database DateTaken if S3 doesn't have it
                    if (catImage.DateTaken == DateTime.MinValue && dbImage.DateTaken != DateTime.MinValue)
                        catImage.DateTaken = dbImage.DateTaken;
                }

                // Log what was extracted/merged
                _logger.LogDebug("Final merged data for {S3Key}: CatName='{CatName}', Description='{Description}', Age='{Age}', Gender='{Gender}', Breed='{Breed}'",
                    s3Object.Key, catImage.CatName, catImage.Description, catImage.AgeAtPhoto, catImage.Gender, catImage.Breed);

                // Try to extract cat name from folder structure as final fallback
                if (string.IsNullOrEmpty(catImage.CatName))
                {
                    var fallbackName = ExtractCatNameFromPath(s3Object.Key);
                    catImage.CatName = fallbackName;
                    _logger.LogDebug("Using fallback cat name for {S3Key}: {FallbackName}", s3Object.Key, fallbackName);
                }

                return catImage;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to process S3 file with DB merge: {S3Key}", s3Object.Key);
                return null;
            }
        }

        private string ExtractS3KeyFromUrl(string imageUrl)
        {
            try
            {
                // Extract S3 key from URL like "https://f004.backblazeb2.com/file/yendor/category/catname/filename.jpg"
                var uri = new Uri(imageUrl);
                var path = uri.AbsolutePath;
                var filePrefix = "/file/yendor/";
                
                if (path.StartsWith(filePrefix))
                {
                    return path.Substring(filePrefix.Length);
                }
                
                return imageUrl;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to extract S3 key from URL: {ImageUrl}", imageUrl);
                return imageUrl;
            }
        }

        private bool IsImageFile(string s3Key)
        {
            if (string.IsNullOrEmpty(s3Key))
                return false;

            var extension = System.IO.Path.GetExtension(s3Key).ToLowerInvariant();
            return extension switch
            {
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".webp" => true,
                _ => false
            };
        }

        private string ExtractCatNameFromPath(string s3Key)
        {
            try
            {
                // Remove key prefix first, then parse: category/catname/filename.jpg
                var relativePath = _s3StorageService.RemoveKeyPrefix(s3Key);
                var parts = relativePath.Split('/');
                if (parts.Length >= 3)
                {
                    var catName = parts[1];
                    if (!string.IsNullOrEmpty(catName) && catName != ".bzEmpty")
                    {
                        // Capitalize first letter
                        return char.ToUpper(catName[0]) + catName.Substring(1);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to extract cat name from S3 key: {Key}", s3Key);
            }

            return "Maine Coon Cat";
        }
    }
}
