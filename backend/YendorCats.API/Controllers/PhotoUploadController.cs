using Microsoft.AspNetCore.Mvc;
using YendorCats.API.Models;
using YendorCats.API.Services;
using YendorCats.API.Data;
using YendorCats.API.Attributes;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace YendorCats.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PhotoUploadController : ControllerBase
    {
        private readonly ILogger<PhotoUploadController> _logger;
        private readonly IS3StorageService _s3StorageService;
        private readonly AppDbContext _context;
        private readonly IImageMetadataService _imageMetadataService;
        private readonly string[] _allowedExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
        private readonly string[] _allowedContentTypes = { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp" };
        private const long MaxFileSize = 10 * 1024 * 1024; // 10MB

        public PhotoUploadController(
            ILogger<PhotoUploadController> logger,
            IS3StorageService s3StorageService,
            AppDbContext context,
            IImageMetadataService imageMetadataService)
        {
            _logger = logger;
            _s3StorageService = s3StorageService;
            _context = context;
            _imageMetadataService = imageMetadataService;
        }

        [HttpPost("upload")]
        [AdminAuthorize("SuperAdmin", "Admin", "Editor")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UploadPhoto(
            [Required] IFormFile file,
            [Required] string catName,
            DateTime? dateTaken = null,
            float? age = null,
            string? gender = null,
            string? breed = null,
            string? hairColor = null,
            string? eyeColor = null,
            string? markings = null,
            string? bloodline = null,
            string? tags = null,
            string? notes = null,
            string category = "gallery")
        {
            try
            {
                var validationResult = ValidateUploadRequest(file, catName, category);
                if (validationResult != null)
                {
                    return validationResult;
                }

                _logger.LogInformation("Starting photo upload for cat: {CatName}, Category: {Category}", catName, category);

                // First, extract IPTC metadata from the image file
                CatImageMetadata iptcMetadata;
                using (var metadataStream = file.OpenReadStream())
                {
                    iptcMetadata = await _imageMetadataService.ExtractCatMetadataAsync(metadataStream, file.FileName);
                }

                // Create metadata object, prioritizing IPTC data over form parameters
                var metadata = new CatImageMetadata
                {
                    // Use IPTC data if available, otherwise fall back to form parameters
                    Name = !string.IsNullOrEmpty(iptcMetadata.Name) ? iptcMetadata.Name : catName.Trim(),
                    DateTaken = iptcMetadata.DateTaken != default ? iptcMetadata.DateTaken : (dateTaken ?? DateTime.UtcNow),
                    Age = !string.IsNullOrEmpty(iptcMetadata.Age) ? iptcMetadata.Age : age?.ToString(),
                    Gender = gender?.Trim().ToUpper() ?? "",
                    Breed = breed?.Trim() ?? "",
                    HairColor = hairColor?.Trim() ?? "",
                    Bloodline = bloodline?.Trim() ?? "",
                    Tags = !string.IsNullOrEmpty(iptcMetadata.Tags) ? iptcMetadata.Tags : tags?.Trim() ?? "",
                    Description = !string.IsNullOrEmpty(iptcMetadata.Description) ? iptcMetadata.Description : notes?.Trim() ?? "",
                    Category = category,
                    FileFormat = Path.GetExtension(file.FileName).ToLower(),
                    ContentType = file.ContentType,
                    FileSize = file.Length,
                    UploaderIp = HttpContext.Connection.RemoteIpAddress?.ToString(),
                    UploaderUserAgent = HttpContext.Request.Headers["User-Agent"].ToString(),
                    UploadSessionId = Guid.NewGuid().ToString()
                };

                // Merge any additional IPTC metadata
                foreach (var kvp in iptcMetadata.AdditionalMetadata)
                {
                    metadata.AdditionalMetadata[kvp.Key] = kvp.Value;
                }

                _logger.LogInformation("Extracted IPTC metadata - Name: {IptcName}, Age: {IptcAge}, Date: {IptcDate}",
                    iptcMetadata.Name, iptcMetadata.Age, iptcMetadata.DateTaken);

                var timestamp = DateTime.UtcNow.ToString("yyyyMMdd-HHmmss");
                var sanitizedCatName = SanitizeFileName(catName);
                var fileExtension = Path.GetExtension(file.FileName).ToLower();
                var uniqueFileName = $"{category}/{sanitizedCatName}-{timestamp}-{Guid.NewGuid().ToString("N")[..8]}{fileExtension}";

                using var fileStream = file.OpenReadStream();
                var s3Metadata = metadata.ToS3Metadata();
                var fileUrl = await _s3StorageService.UploadFileWithMetadataAsync(
                    fileStream, uniqueFileName, file.ContentType, s3Metadata);

                var catProfile = await GetOrCreateCatProfile(catName, metadata);

                var galleryImage = new CatGalleryImage
                {
                    Id = 0, // Let database generate ID
                    CatName = metadata.Name, // Use extracted metadata name
                    Age = !string.IsNullOrEmpty(metadata.Age) && float.TryParse(metadata.Age, out var parsedAge) ? parsedAge : (age ?? 0),
                    DateTaken = metadata.DateTaken ?? DateTime.UtcNow, // Use extracted metadata date
                    Gender = gender?.Trim().ToUpper() ?? "",
                    Breed = breed?.Trim() ?? "",
                    Color = hairColor?.Trim() ?? "",
                    Bloodline = bloodline?.Trim() ?? "",
                    Tags = metadata.Tags ?? "", // Use extracted metadata tags
                    Description = metadata.Description ?? "", // Use extracted metadata description
                    Category = category,
                    ImageUrl = fileUrl,
                    OrderNumber = 1
                };

                // Save the gallery image with extracted metadata to database
                _context.CatGalleryImages.Add(galleryImage);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Saved gallery image to database: {ImageId}, Cat: {CatName}, Category: {Category}",
                    galleryImage.Id, galleryImage.CatName, galleryImage.Category);

                var result = new
                {
                    success = true,
                    message = "Photo uploaded successfully",
                    data = new
                    {
                        photoId = galleryImage.Id,
                        catName = galleryImage.CatName,
                        fileName = Path.GetFileName(uniqueFileName),
                        fileUrl = fileUrl,
                        category = category,
                        dateTaken = galleryImage.DateTaken,
                        fileSize = file.Length,
                        catProfileId = catProfile?.Id,
                        metadata = galleryImage
                    }
                };

                _logger.LogInformation("Photo uploaded successfully for cat: {CatName}, File: {FileName}, URL: {FileUrl}",
                    catName, uniqueFileName, fileUrl);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading photo for cat: {CatName}", catName);
                return StatusCode(500, new {
                    success = false,
                    message = "An error occurred while uploading the photo",
                    error = ex.Message
                });
            }
        }

        [HttpGet("cat/{catName}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetCatPhotos(
            string catName,
            string orderBy = "date",
            bool descending = true)
        {
            try
            {
                _logger.LogInformation("Getting photos for cat: {CatName}", catName);

                var catProfile = await _context.Cats
                    .Include(c => c.Images)
                    .FirstOrDefaultAsync(c => c.Name.ToLower() == catName.ToLower());

                var photos = await GetCatPhotosFromS3(catName, orderBy, descending);

                var result = new
                {
                    success = true,
                    catName = catName,
                    photoCount = photos.Count,
                    catProfile = catProfile != null ? new
                    {
                        id = catProfile.Id,
                        name = catProfile.Name,
                        breed = catProfile.Breed,
                        gender = catProfile.Gender,
                        dateOfBirth = catProfile.DateOfBirth,
                        color = catProfile.Color,
                        description = catProfile.Description,
                        markings = catProfile.Markings,
                        isAvailable = catProfile.IsAvailable
                    } : null,
                    photos = photos,
                    timeline = CreatePhotoTimeline(photos)
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting photos for cat: {CatName}", catName);
                return StatusCode(500, new {
                    success = false,
                    message = "An error occurred while retrieving cat photos",
                    error = ex.Message
                });
            }
        }

        [HttpGet("profiles")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCatProfiles()
        {
            try
            {
                _logger.LogInformation("Getting all cat profiles");

                var cats = await _context.Cats
                    .Include(c => c.Images)
                    .Where(c => c.IsActive)
                    .ToListAsync();

                var s3CatNames = await GetUniqueCatNamesFromS3();

                var profiles = new List<object>();

                foreach (var cat in cats)
                {
                    var s3PhotoCount = await GetCatPhotoCountFromS3(cat.Name);
                    profiles.Add(new
                    {
                        id = cat.Id,
                        name = cat.Name,
                        breed = cat.Breed,
                        gender = cat.Gender,
                        dateOfBirth = cat.DateOfBirth,
                        color = cat.Color,
                        description = cat.Description,
                        isAvailable = cat.IsAvailable,
                        databasePhotoCount = cat.Images?.Count ?? 0,
                        s3PhotoCount = s3PhotoCount,
                        totalPhotoCount = s3PhotoCount,
                        hasProfile = true
                    });
                }

                foreach (var catName in s3CatNames)
                {
                    if (!cats.Any(c => c.Name.ToLower() == catName.ToLower()))
                    {
                        var photoCount = await GetCatPhotoCountFromS3(catName);
                        profiles.Add(new
                        {
                            id = (int?)null,
                            name = catName,
                            breed = (string?)null,
                            gender = (string?)null,
                            dateOfBirth = (DateTime?)null,
                            color = (string?)null,
                            description = (string?)null,
                            isAvailable = false,
                            databasePhotoCount = 0,
                            s3PhotoCount = photoCount,
                            totalPhotoCount = photoCount,
                            hasProfile = false
                        });
                    }
                }

                var result = new
                {
                    success = true,
                    profileCount = profiles.Count,
                    profiles = profiles.OrderBy(p => ((dynamic)p).name)
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cat profiles");
                return StatusCode(500, new {
                    success = false,
                    message = "An error occurred while retrieving cat profiles",
                    error = ex.Message
                });
            }
        }

        private IActionResult? ValidateUploadRequest(IFormFile file, string catName, string category)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { success = false, message = "No file provided" });
            }

            if (file.Length > MaxFileSize)
            {
                return BadRequest(new { success = false, message = $"File size exceeds maximum allowed size of {MaxFileSize / (1024 * 1024)}MB" });
            }

            if (string.IsNullOrWhiteSpace(catName))
            {
                return BadRequest(new { success = false, message = "Cat name is required" });
            }

            var fileExtension = Path.GetExtension(file.FileName).ToLower();
            if (!_allowedExtensions.Contains(fileExtension))
            {
                return BadRequest(new { success = false, message = $"File type not allowed. Allowed types: {string.Join(", ", _allowedExtensions)}" });
            }

            if (!_allowedContentTypes.Contains(file.ContentType.ToLower()))
            {
                return BadRequest(new { success = false, message = $"Content type not allowed. Allowed types: {string.Join(", ", _allowedContentTypes)}" });
            }

            var validCategories = new[] { "gallery", "studs", "queens", "kittens" };
            if (!validCategories.Contains(category.ToLower()))
            {
                return BadRequest(new { success = false, message = $"Invalid category. Valid categories: {string.Join(", ", validCategories)}" });
            }

            return null;
        }

        private string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
            return sanitized.Replace(" ", "-").ToLower();
        }

        private async Task<Cat?> GetOrCreateCatProfile(string catName, CatImageMetadata metadata)
        {
            try
            {
                var existingCat = await _context.Cats
                    .FirstOrDefaultAsync(c => c.Name.ToLower() == catName.ToLower());

                if (existingCat != null)
                {
                    _logger.LogInformation("Found existing cat profile for: {CatName}", catName);
                    return existingCat;
                }

                if (!string.IsNullOrEmpty(metadata.Breed) && !string.IsNullOrEmpty(metadata.Gender))
                {
                    var ageInYears = 1;
                    if (!string.IsNullOrEmpty(metadata.Age) && float.TryParse(metadata.Age, out var parsedAge))
                    {
                        ageInYears = (int)parsedAge;
                    }

                    var newCat = new Cat
                    {
                        Name = catName.Trim(),
                        Breed = metadata.Breed,
                        Gender = metadata.Gender,
                        Color = metadata.HairColor ?? "Unknown",
                        DateOfBirth = DateTime.UtcNow.AddYears(-ageInYears),
                        Description = metadata.Description,
                        Price = 0,
                        IsAvailable = false,
                        IsActive = true
                    };

                    _context.Cats.Add(newCat);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Created new cat profile for: {CatName}", catName);
                    return newCat;
                }

                _logger.LogInformation("Not enough information to create cat profile for: {CatName}", catName);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting or creating cat profile for: {CatName}", catName);
                return null;
            }
        }

        private async Task<List<CatGalleryImage>> GetCatPhotosFromS3(string catName, string orderBy, bool descending)
        {
            var photos = new List<CatGalleryImage>();
            var sanitizedCatName = SanitizeFileName(catName);
            var prefixes = new[] { "studs/", "queens/", "kittens/", "gallery/" };

            foreach (var prefix in prefixes)
            {
                var s3Objects = await _s3StorageService.ListFilesAsync(prefix);
                foreach (var s3Object in s3Objects)
                {
                    if (s3Object.Key.ToLower().Contains(sanitizedCatName))
                    {
                        var metadata = await _s3StorageService.GetObjectMetadataAsync(s3Object.Key);
                        var imageUrl = $"https://f004.backblazeb2.com/file/yendor/YendorCats-General-SiteAccess/{s3Object.Key}";
                        var catImage = CatGalleryImage.FromS3Metadata(metadata, imageUrl, prefix.Trim('/'), null);
                        photos.Add(catImage);
                    }
                }
            }

            return photos;
        }

        private async Task<List<string>> GetUniqueCatNamesFromS3()
        {
            var catNames = new HashSet<string>();
            var prefixes = new[] { "studs/", "queens/", "kittens/", "gallery/" };

            foreach (var prefix in prefixes)
            {
                var s3Objects = await _s3StorageService.ListFilesAsync(prefix);
                foreach (var s3Object in s3Objects)
                {
                    var metadata = await _s3StorageService.GetObjectMetadataAsync(s3Object.Key);
                    if (metadata.TryGetValue("name", out var name))
                    {
                        catNames.Add(name);
                    }
                }
            }

            return catNames.ToList();
        }

        private async Task<int> GetCatPhotoCountFromS3(string catName)
        {
            var count = 0;
            var sanitizedCatName = SanitizeFileName(catName);
            var prefixes = new[] { "studs/", "queens/", "kittens/", "gallery/" };

            foreach (var prefix in prefixes)
            {
                var s3Objects = await _s3StorageService.ListFilesAsync(prefix);
                count += s3Objects.Count(s3Object => s3Object.Key.ToLower().Contains(sanitizedCatName));
            }

            return count;
        }

        private object CreatePhotoTimeline(List<CatGalleryImage> photos)
        {
            try
            {
                var timeline = new
                {
                    totalPhotos = photos.Count,
                    dateRange = photos.Count > 0 ? new
                    {
                        earliest = photos.Min(p => p.DateTaken),
                        latest = photos.Max(p => p.DateTaken)
                    } : null,
                    monthlyBreakdown = photos
                        .Where(p => p.DateTaken.HasValue)
                        .GroupBy(p => new { p.DateTaken!.Value.Year, p.DateTaken!.Value.Month })
                        .Select(g => new {
                            month = new DateTime(g.Key.Year, g.Key.Month, 1),
                            count = g.Count()
                        })
                        .OrderBy(g => g.month)
                        .ToList()
                };

                return timeline;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating photo timeline");
                return new { totalPhotos = 0, dateRange = (object?)null, monthlyBreakdown = new List<object>() };
            }
        }
    }
}
