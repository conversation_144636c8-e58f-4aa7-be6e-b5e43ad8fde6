﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace YendorCats.API.Migrations
{
    /// <inheritdoc />
    public partial class FixNullGenderValues : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Fix any existing CatProfile records with null Gender values
            migrationBuilder.Sql(@"
                UPDATE CatProfiles
                SET Gender = 'U'
                WHERE Gender IS NULL OR Gender = '';
            ");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_FatherId",
                table: "CatProfiles",
                column: "FatherId");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_MotherId",
                table: "CatProfiles",
                column: "MotherId");

            migrationBuilder.AddForeignKey(
                name: "FK_CatProfiles_CatProfiles_FatherId",
                table: "CatProfiles",
                column: "FatherId",
                principalTable: "CatProfiles",
                principalColumn: "CatId",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_CatProfiles_CatProfiles_MotherId",
                table: "CatProfiles",
                column: "MotherId",
                principalTable: "CatProfiles",
                principalColumn: "CatId",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CatProfiles_CatProfiles_FatherId",
                table: "CatProfiles");

            migrationBuilder.DropForeignKey(
                name: "FK_CatProfiles_CatProfiles_MotherId",
                table: "CatProfiles");

            migrationBuilder.DropIndex(
                name: "IX_CatProfiles_FatherId",
                table: "CatProfiles");

            migrationBuilder.DropIndex(
                name: "IX_CatProfiles_MotherId",
                table: "CatProfiles");
        }
    }
}
