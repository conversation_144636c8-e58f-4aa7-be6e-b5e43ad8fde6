﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using YendorCats.API.Data;

#nullable disable

namespace YendorCats.API.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250805025255_FixNullGenderValues")]
    partial class FixNullGenderValues
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.11");

            modelBuilder.Entity("YendorCats.API.Models.AdminUser", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("FailedLoginAttempts")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("LockedUntil")
                        .HasColumnType("TEXT");

                    b.Property<bool>("MustChangePassword")
                        .HasColumnType("INTEGER");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("PasswordLastChanged")
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordSalt")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("AdminUsers");
                });

            modelBuilder.Entity("YendorCats.API.Models.Appointment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("CatId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ClientId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("TEXT");

                    b.Property<string>("Location")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<bool>("ReminderSent")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ReminderSentAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CatId");

                    b.HasIndex("ClientId");

                    b.HasIndex("StartTime");

                    b.HasIndex("Status");

                    b.ToTable("Appointments");
                });

            modelBuilder.Entity("YendorCats.API.Models.B2SyncLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("B2Bucket")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("B2FileId")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("B2Key")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("BatchId")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int?>("BatchSequence")
                        .HasColumnType("INTEGER");

                    b.Property<long?>("CatGalleryImageId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("CatId")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CatProfileId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Category")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Checksum")
                        .HasMaxLength(64)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("ContentType")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Context")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("DestinationBucket")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("DestinationFileId")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("DestinationKey")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("DestinationProvider")
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.Property<int?>("DurationMs")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ErrorCode")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("ErrorDetails")
                        .HasMaxLength(4000)
                        .HasColumnType("TEXT");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<string>("FileHash")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<long?>("FileSize")
                        .HasColumnType("INTEGER");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsVerified")
                        .HasColumnType("INTEGER");

                    b.Property<int>("MaxRetries")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("MetadataFieldCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("MetadataJson")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("MigrationId")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("NextRetryAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Operation")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("OriginalFileName")
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<int?>("ProcessingTimeMs")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("RequiresManualReview")
                        .HasColumnType("INTEGER");

                    b.Property<int>("RetryCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<string>("ReviewNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("S3Bucket")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("S3Key")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("SessionId")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("SourceBucket")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("SourceKey")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("SourceProvider")
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("StorageKey")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("SyncedAt")
                        .HasColumnType("TEXT");

                    b.Property<int?>("TotalBatchItems")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("VerifiedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("VerifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("B2FileId");

                    b.HasIndex("B2Key");

                    b.HasIndex("BatchId");

                    b.HasIndex("CatGalleryImageId");

                    b.HasIndex("CatProfileId");

                    b.HasIndex("CompletedAt");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("FileName");

                    b.HasIndex("Operation");

                    b.HasIndex("S3Key");

                    b.HasIndex("Status");

                    b.HasIndex("BatchId", "Status");

                    b.HasIndex("Operation", "Status");

                    b.HasIndex("Status", "CreatedAt");

                    b.HasIndex("Operation", "Status", "CreatedAt");

                    b.ToTable("B2SyncLogs");
                });

            modelBuilder.Entity("YendorCats.API.Models.Cat", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Breed")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CatId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Color")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateOfBirth")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int?>("FatherId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Gender")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsAvailable")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Markings")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int?>("MotherId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Price")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CatId");

                    b.HasIndex("FatherId");

                    b.HasIndex("MotherId");

                    b.ToTable("Cats");
                });

            modelBuilder.Entity("YendorCats.API.Models.CatGalleryImage", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("AccessCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("AgeAtPhoto")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Alt")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<float?>("AspectRatio")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("REAL");

                    b.Property<string>("B2Bucket")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("B2FileId")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("B2Key")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("B2Url")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Bloodline")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Breed")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("CatId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("CatName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Color")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateTaken")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateUploaded")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<int>("DownloadCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<string>("ExifData")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<string>("FileFormat")
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.Property<long>("FileSize")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Filename")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("Format")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Gender")
                        .HasMaxLength(1)
                        .HasColumnType("TEXT");

                    b.Property<int>("Height")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsFeatured")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsPublic")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastAccessedAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("LastViewedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("LikeCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<string>("MimeType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("Personality")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("S3Bucket")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("S3Key")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("S3Url")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<int>("SortOrder")
                        .HasColumnType("INTEGER");

                    b.Property<string>("StorageBucketName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("StorageFileId")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("StorageKey")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("StorageProvider")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Tags")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("ThumbnailStorageKey")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("Title")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Traits")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("ViewCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<int>("Width")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("B2Key");

                    b.HasIndex("CatId");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DisplayOrder");

                    b.HasIndex("Filename")
                        .IsUnique();

                    b.HasIndex("IsActive");

                    b.HasIndex("IsFeatured");

                    b.HasIndex("IsPublic");

                    b.HasIndex("S3Key");

                    b.HasIndex("StorageProvider");

                    b.HasIndex("ViewCount");

                    b.HasIndex("IsFeatured", "DisplayOrder");

                    b.HasIndex("StorageProvider", "IsActive");

                    b.HasIndex("CatId", "IsActive", "IsPublic");

                    b.ToTable("CatGalleryImages");
                });

            modelBuilder.Entity("YendorCats.API.Models.CatImage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Caption")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("CatId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<long>("FileSize")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("Height")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("TEXT");

                    b.Property<int?>("Width")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("CatId");

                    b.ToTable("CatImages");
                });

            modelBuilder.Entity("YendorCats.API.Models.CatProfile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AvailabilityStatus")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Awards")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("BehaviorTraits")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("BirthDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Bloodline")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("BloodlineType")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Breed")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("BreedingNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("BreedingStatus")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("CaregiverContact")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("CaregiverName")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("CatId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("CatName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<int?>("CatProfileId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ChampionTitles")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("Color")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("CurrentLocation")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("CurrentValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("DamId")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("DamName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0);

                    b.Property<string>("EyeColor")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("FatherId")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("FirstBreedingDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Gender")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("TEXT");

                    b.Property<string>("GeneticTesting")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("HealthNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("HealthRecords")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("HealthStatus")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("Height")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsBreeding")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsFeatured")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsPublic")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsRetired")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsStudService")
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("KittenPrice")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("LastBreedingDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("LastHealthCheck")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("Length")
                        .HasColumnType("TEXT");

                    b.Property<int>("LikeCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Markings")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Microchip")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("MicrochipNumber")
                        .HasMaxLength(15)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("MotherId")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<int>("OffspringCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("OwnerInfo")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("Pattern")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Pedigree")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Personality")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("PersonalityDescription")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("PricingNotes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("ProfileImage")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("ProfileImageStorageKey")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("ProfileImageStorageProvider")
                        .HasMaxLength(10)
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("PurchasePrice")
                        .HasColumnType("TEXT");

                    b.Property<string>("RegisteredName")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("RegistrationBody")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("RegistrationNumber")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("SireId")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("SireName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("SpecialNeeds")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("StudFee")
                        .HasColumnType("TEXT");

                    b.Property<string>("Tags")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Tattoo")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Titles")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int?>("TotalLitters")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Vaccinations")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("VetInfo")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("VeterinarianContact")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int>("ViewCount")
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("Weight")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("AvailabilityStatus");

                    b.HasIndex("BirthDate");

                    b.HasIndex("Bloodline");

                    b.HasIndex("Breed");

                    b.HasIndex("BreedingStatus");

                    b.HasIndex("CatId")
                        .IsUnique();

                    b.HasIndex("CatName");

                    b.HasIndex("CatProfileId");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DamId");

                    b.HasIndex("DisplayOrder");

                    b.HasIndex("FatherId");

                    b.HasIndex("Gender");

                    b.HasIndex("IsActive");

                    b.HasIndex("IsFeatured");

                    b.HasIndex("IsPublic");

                    b.HasIndex("MotherId");

                    b.HasIndex("SireId");

                    b.HasIndex("IsFeatured", "DisplayOrder");

                    b.HasIndex("SireId", "DamId");

                    b.HasIndex("AvailabilityStatus", "Gender", "IsActive");

                    b.HasIndex("Breed", "IsActive", "IsPublic");

                    b.HasIndex("Gender", "BreedingStatus", "IsActive");

                    b.ToTable("CatProfiles");
                });

            modelBuilder.Entity("YendorCats.API.Models.Client", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("City")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Country")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Phone")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("State")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Email");

                    b.HasIndex("FirstName");

                    b.HasIndex("LastName");

                    b.ToTable("Clients");
                });

            modelBuilder.Entity("YendorCats.API.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordSalt")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("RefreshToken")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("RefreshTokenExpiryTime")
                        .HasColumnType("TEXT");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("YendorCats.API.Models.Appointment", b =>
                {
                    b.HasOne("YendorCats.API.Models.Cat", "Cat")
                        .WithMany()
                        .HasForeignKey("CatId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("YendorCats.API.Models.Client", "Client")
                        .WithMany("Appointments")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Cat");

                    b.Navigation("Client");
                });

            modelBuilder.Entity("YendorCats.API.Models.B2SyncLog", b =>
                {
                    b.HasOne("YendorCats.API.Models.CatGalleryImage", "CatGalleryImage")
                        .WithMany()
                        .HasForeignKey("CatGalleryImageId");

                    b.HasOne("YendorCats.API.Models.CatProfile", "CatProfile")
                        .WithMany()
                        .HasForeignKey("CatProfileId");

                    b.Navigation("CatGalleryImage");

                    b.Navigation("CatProfile");
                });

            modelBuilder.Entity("YendorCats.API.Models.Cat", b =>
                {
                    b.HasOne("YendorCats.API.Models.Cat", null)
                        .WithMany("Offspring")
                        .HasForeignKey("CatId");

                    b.HasOne("YendorCats.API.Models.Cat", "Father")
                        .WithMany()
                        .HasForeignKey("FatherId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("YendorCats.API.Models.Cat", "Mother")
                        .WithMany()
                        .HasForeignKey("MotherId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Father");

                    b.Navigation("Mother");
                });

            modelBuilder.Entity("YendorCats.API.Models.CatGalleryImage", b =>
                {
                    b.HasOne("YendorCats.API.Models.CatProfile", "CatProfile")
                        .WithMany("GalleryImages")
                        .HasForeignKey("CatId")
                        .HasPrincipalKey("CatId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("CatProfile");
                });

            modelBuilder.Entity("YendorCats.API.Models.CatImage", b =>
                {
                    b.HasOne("YendorCats.API.Models.Cat", "Cat")
                        .WithMany("Images")
                        .HasForeignKey("CatId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Cat");
                });

            modelBuilder.Entity("YendorCats.API.Models.CatProfile", b =>
                {
                    b.HasOne("YendorCats.API.Models.CatProfile", null)
                        .WithMany("Offspring")
                        .HasForeignKey("CatProfileId");

                    b.HasOne("YendorCats.API.Models.CatProfile", "Father")
                        .WithMany()
                        .HasForeignKey("FatherId")
                        .HasPrincipalKey("CatId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("YendorCats.API.Models.CatProfile", "Mother")
                        .WithMany()
                        .HasForeignKey("MotherId")
                        .HasPrincipalKey("CatId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Father");

                    b.Navigation("Mother");
                });

            modelBuilder.Entity("YendorCats.API.Models.Cat", b =>
                {
                    b.Navigation("Images");

                    b.Navigation("Offspring");
                });

            modelBuilder.Entity("YendorCats.API.Models.CatProfile", b =>
                {
                    b.Navigation("GalleryImages");

                    b.Navigation("Offspring");
                });

            modelBuilder.Entity("YendorCats.API.Models.Client", b =>
                {
                    b.Navigation("Appointments");
                });
#pragma warning restore 612, 618
        }
    }
}
