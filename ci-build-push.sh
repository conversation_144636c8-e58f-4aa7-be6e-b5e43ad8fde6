#!/bin/bash

# ============================================================================
# Automated CI/CD Build & Push Script for Yendor Cats
# ============================================================================
# This script handles the complete build and push process with proper git SHA tagging
# It ensures cache busting and smooth deployments without manual intervention
#
# Usage: ./ci-build-push.sh [--force-rebuild]
# ============================================================================

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
AWS_REGION="${AWS_REGION:-ap-southeast-2}"
AWS_ACCOUNT_ID="${AWS_ACCOUNT_ID:-************}"
ECR_REGISTRY="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"

# Services to build
SERVICES=("api" "frontend" "uploader")

# Function to print colored messages
print_message() {
    echo -e "${2}${1}${NC}"
}

# Function to print section headers
print_header() {
    echo ""
    echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${CYAN}  $1${NC}"
    echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo ""
}

# Function to check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check for required tools
    local tools=("git" "docker" "aws" "jq")
    for tool in "${tools[@]}"; do
        if ! command -v $tool &> /dev/null; then
            print_message "❌ $tool is not installed" "$RED"
            exit 1
        fi
        print_message "✓ $tool is installed" "$GREEN"
    done
    
    # Check git repository
    if [ ! -d .git ]; then
        print_message "❌ Not in a git repository" "$RED"
        exit 1
    fi
    print_message "✓ Git repository detected" "$GREEN"
    
    # Check for uncommitted changes
    if [ -n "$(git status --porcelain)" ]; then
        print_message "⚠️  Warning: You have uncommitted changes" "$YELLOW"
        print_message "   These changes will NOT be included in the build" "$YELLOW"
        read -p "   Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_message "Build cancelled" "$YELLOW"
            exit 0
        fi
    fi
}

# Function to get git information
get_git_info() {
    print_header "Getting Git Information"
    
    # Get current commit SHA (short version for tagging)
    GIT_SHA=$(git rev-parse --short HEAD)
    GIT_SHA_FULL=$(git rev-parse HEAD)
    
    # Get current branch
    GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
    
    # Get commit timestamp
    GIT_TIMESTAMP=$(git show -s --format=%cI HEAD)
    
    # Create a clean branch name for tagging (replace / with -)
    BRANCH_TAG=$(echo "$GIT_BRANCH" | sed 's/\//-/g')
    
    print_message "Git SHA (short): $GIT_SHA" "$GREEN"
    print_message "Git SHA (full): $GIT_SHA_FULL" "$GREEN"
    print_message "Git Branch: $GIT_BRANCH" "$GREEN"
    print_message "Branch Tag: $BRANCH_TAG" "$GREEN"
    print_message "Timestamp: $GIT_TIMESTAMP" "$GREEN"
}

# Function to login to ECR
login_to_ecr() {
    print_header "Logging into AWS ECR"
    
    # Check AWS credentials
    if ! aws sts get-caller-identity --region $AWS_REGION &> /dev/null; then
        print_message "❌ AWS credentials not configured or invalid" "$RED"
        print_message "   Please configure AWS CLI with: aws configure" "$YELLOW"
        exit 1
    fi
    
    # Get ECR login token
    print_message "Getting ECR login token..." "$YELLOW"
    aws ecr get-login-password --region $AWS_REGION | \
        docker login --username AWS --password-stdin $ECR_REGISTRY
    
    if [ $? -eq 0 ]; then
        print_message "✓ Successfully logged into ECR" "$GREEN"
    else
        print_message "❌ Failed to login to ECR" "$RED"
        exit 1
    fi
}

# Function to ensure ECR repositories exist
ensure_ecr_repos() {
    print_header "Ensuring ECR Repositories Exist"
    
    for service in "${SERVICES[@]}"; do
        local repo_name="yendorcats-${service}"
        
        # Check if repository exists
        if aws ecr describe-repositories --repository-names $repo_name --region $AWS_REGION &> /dev/null; then
            print_message "✓ Repository $repo_name exists" "$GREEN"
        else
            print_message "Creating repository $repo_name..." "$YELLOW"
            aws ecr create-repository --repository-name $repo_name --region $AWS_REGION > /dev/null
            print_message "✓ Created repository $repo_name" "$GREEN"
        fi
    done
}

# Function to build Docker images
build_images() {
    print_header "Building Docker Images"
    
    local force_rebuild=""
    if [[ "$1" == "--force-rebuild" ]]; then
        force_rebuild="--no-cache"
        print_message "Force rebuild enabled (--no-cache)" "$YELLOW"
    fi
    
    # Build API
    print_message "Building yendorcats-api..." "$BLUE"
    docker build $force_rebuild \
        -f backend/YendorCats.API/Dockerfile \
        -t ${ECR_REGISTRY}/yendorcats-api:${GIT_SHA} \
        -t ${ECR_REGISTRY}/yendorcats-api:${BRANCH_TAG} \
        -t ${ECR_REGISTRY}/yendorcats-api:latest \
        --build-arg GIT_SHA=${GIT_SHA} \
        --build-arg GIT_BRANCH=${GIT_BRANCH} \
        --build-arg BUILD_TIMESTAMP="${GIT_TIMESTAMP}" \
        .
    print_message "✓ Built yendorcats-api" "$GREEN"
    
    # Build Frontend with cache busting
    print_message "Building yendorcats-frontend..." "$BLUE"
    
    # Create a temporary build context with git SHA for cache busting
    cat > frontend/.build-info.json << EOF
{
    "gitSha": "${GIT_SHA}",
    "gitShaFull": "${GIT_SHA_FULL}",
    "gitBranch": "${GIT_BRANCH}",
    "buildTimestamp": "${GIT_TIMESTAMP}",
    "buildDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF
    
    docker build $force_rebuild \
        -f Dockerfile.frontend.ci \
        -t ${ECR_REGISTRY}/yendorcats-frontend:${GIT_SHA} \
        -t ${ECR_REGISTRY}/yendorcats-frontend:${BRANCH_TAG} \
        -t ${ECR_REGISTRY}/yendorcats-frontend:latest \
        --build-arg ASSET_VERSION=${GIT_SHA} \
        --build-arg GIT_SHA=${GIT_SHA} \
        --build-arg GIT_BRANCH=${GIT_BRANCH} \
        .
    print_message "✓ Built yendorcats-frontend" "$GREEN"
    
    # Build Uploader
    print_message "Building yendorcats-uploader..." "$BLUE"
    docker build $force_rebuild \
        -f tools/file-uploader/Dockerfile \
        -t ${ECR_REGISTRY}/yendorcats-uploader:${GIT_SHA} \
        -t ${ECR_REGISTRY}/yendorcats-uploader:${BRANCH_TAG} \
        -t ${ECR_REGISTRY}/yendorcats-uploader:latest \
        tools/file-uploader
    print_message "✓ Built yendorcats-uploader" "$GREEN"
}

# Function to push images to ECR
push_images() {
    print_header "Pushing Images to ECR"
    
    for service in "${SERVICES[@]}"; do
        local image_name="yendorcats-${service}"
        
        print_message "Pushing ${image_name}:${GIT_SHA}..." "$BLUE"
        docker push ${ECR_REGISTRY}/${image_name}:${GIT_SHA}
        
        print_message "Pushing ${image_name}:${BRANCH_TAG}..." "$BLUE"
        docker push ${ECR_REGISTRY}/${image_name}:${BRANCH_TAG}
        
        print_message "Pushing ${image_name}:latest..." "$BLUE"
        docker push ${ECR_REGISTRY}/${image_name}:latest
        
        print_message "✓ Pushed all tags for ${image_name}" "$GREEN"
    done
}

# Function to create deployment manifest
create_deployment_manifest() {
    print_header "Creating Deployment Manifest"
    
    local manifest_file="deployment-manifest.json"
    
    cat > $manifest_file << EOF
{
    "version": "1.0",
    "deployment": {
        "gitSha": "${GIT_SHA}",
        "gitShaFull": "${GIT_SHA_FULL}",
        "gitBranch": "${GIT_BRANCH}",
        "buildTimestamp": "${GIT_TIMESTAMP}",
        "deploymentTimestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "images": {
            "api": "${ECR_REGISTRY}/yendorcats-api:${GIT_SHA}",
            "frontend": "${ECR_REGISTRY}/yendorcats-frontend:${GIT_SHA}",
            "uploader": "${ECR_REGISTRY}/yendorcats-uploader:${GIT_SHA}"
        },
        "tags": {
            "sha": "${GIT_SHA}",
            "branch": "${BRANCH_TAG}",
            "latest": "latest"
        }
    }
}
EOF
    
    print_message "✓ Created deployment manifest: $manifest_file" "$GREEN"
    
    # Also create a simple version file for the server
    echo "$GIT_SHA" > .latest-deployment-tag
    print_message "✓ Created .latest-deployment-tag file" "$GREEN"
}

# Function to show deployment instructions
show_deployment_instructions() {
    print_header "Deployment Instructions"
    
    print_message "Images have been successfully built and pushed to ECR!" "$GREEN"
    echo ""
    print_message "To deploy on your server, run:" "$CYAN"
    echo ""
    echo "    ssh ubuntu@your-server"
    echo "    cd /home/<USER>/yendorcats.com"
    echo "    ./ci-deploy.sh $GIT_SHA"
    echo ""
    print_message "Or for automatic deployment of latest:" "$CYAN"
    echo ""
    echo "    ./ci-deploy.sh --latest"
    echo ""
    print_message "Tagged images available:" "$YELLOW"
    echo "  - ${GIT_SHA} (commit SHA)"
    echo "  - ${BRANCH_TAG} (branch)"
    echo "  - latest"
}

# Main execution
main() {
    print_header "Yendor Cats CI/CD Build & Push"
    
    # Parse arguments
    local force_rebuild=""
    if [[ "$1" == "--force-rebuild" ]]; then
        force_rebuild="--force-rebuild"
    fi
    
    # Run all steps
    check_prerequisites
    get_git_info
    login_to_ecr
    ensure_ecr_repos
    build_images $force_rebuild
    push_images
    create_deployment_manifest
    show_deployment_instructions
    
    print_header "Build Complete!"
    print_message "All images built and pushed successfully with tag: $GIT_SHA" "$GREEN"
}

# Run main function
main "$@"
