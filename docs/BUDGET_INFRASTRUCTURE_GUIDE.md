---
title: "Budget-Friendly Infrastructure Setup Guide"
tags: [infrastructure, budget, vps, docker, scaling, portainer, traefik]
created: 2025-08-03
updated: 2025-08-03
---

# Budget-Friendly Infrastructure Setup Guide

## VPS Provider Comparison

### #budget #vps #providers #comparison

| Provider | Plan | vCPU | RAM | Storage | Bandwidth | Price/Month | Best For |
|----------|------|------|-----|---------|-----------|-------------|----------|
| **Hetzner** | CPX11 | 2 | 4GB | 40GB SSD | 20TB | €4.15 (~$4.50) | **Best Value** |
| **Hetzner** | CPX21 | 3 | 4GB | 80GB SSD | 20TB | €6.90 (~$7.50) | **Recommended** |
| **Vultr** | Regular | 1 | 1GB | 25GB SSD | 1TB | $6/month | Starter |
| **Vultr** | Regular | 2 | 4GB | 80GB SSD | 3TB | $12/month | Production Ready |
| **OVH** | VPS SSD 1 | 1 | 2GB | 20GB SSD | Unlimited | €3.50 (~$3.80) | Minimal |
| **OVH** | VPS SSD 2 | 2 | 4GB | 80GB SSD | Unlimited | €7 (~$7.60) | Balanced |
| **DigitalOcean** | Basic | 1 | 1GB | 25GB SSD | 1TB | $6/month | Easy Setup |

### #recommendation #hetzner #value
**🏆 Recommended: Hetzner CPX21**
- **Best price-to-performance ratio**
- Excellent network (20TB bandwidth)
- European data centers (GDPR compliant)
- Reliable infrastructure
- Easy scaling path

## Single-Server Architecture

### #architecture #docker #compose #production

```yaml
# docker-compose.production.yml
version: '3.8'

services:
  # Reverse Proxy & SSL Termination
  traefik:
    image: traefik:v2.10
    container_name: traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Dashboard (secure with auth)
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik:/etc/traefik:ro
      - ./ssl:/ssl
      - traefik-data:/data
    environment:
      - TRAEFIK_API_DASHBOARD=true
      - TRAEFIK_API_INSECURE=false
      - TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_EMAIL=${ACME_EMAIL}
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`traefik.${DOMAIN}`)"
      - "traefik.http.routers.dashboard.tls.certresolver=letsencrypt"
      - "traefik.http.routers.dashboard.middlewares=auth"
      - "traefik.http.middlewares.auth.basicauth.users=${TRAEFIK_AUTH}"

  # YendorCats API
  api:
    image: yendorcats/api:latest
    container_name: yendorcats-api
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__SqliteConnection=Data Source=/app/data/yendorcats.db
      - AWS__S3__BucketName=${AWS_S3_BUCKET_NAME}
      - AWS__S3__AccessKey=${AWS_S3_ACCESS_KEY}
      - AWS__S3__SecretKey=${AWS_S3_SECRET_KEY}
      - YENDOR_JWT_SECRET=${YENDOR_JWT_SECRET}
    volumes:
      - api-data:/app/data
      - api-logs:/app/logs
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api.rule=Host(`api.${DOMAIN}`)"
      - "traefik.http.routers.api.tls.certresolver=letsencrypt"
      - "traefik.http.services.api.loadbalancer.server.port=80"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Frontend
  frontend:
    image: nginx:alpine
    container_name: yendorcats-frontend
    restart: unless-stopped
    volumes:
      - ./frontend:/usr/share/nginx/html:ro
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`${DOMAIN}`)"
      - "traefik.http.routers.frontend.tls.certresolver=letsencrypt"
      - "traefik.http.services.frontend.loadbalancer.server.port=80"
    depends_on:
      - api
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.25'

  # File Uploader Service
  uploader:
    image: yendorcats/uploader:latest
    container_name: yendorcats-uploader
    restart: unless-stopped
    environment:
      - AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME}
      - AWS_S3_ACCESS_KEY=${AWS_S3_ACCESS_KEY}
      - AWS_S3_SECRET_KEY=${AWS_S3_SECRET_KEY}
      - API_BASE_URL=http://api:80
    volumes:
      - uploader-temp:/app/uploads
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.uploader.rule=Host(`upload.${DOMAIN}`)"
      - "traefik.http.routers.uploader.tls.certresolver=letsencrypt"
    depends_on:
      - api
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # Container Management
  portainer:
    image: portainer/portainer-ce:latest
    container_name: portainer
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer-data:/data
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.portainer.rule=Host(`portainer.${DOMAIN}`)"
      - "traefik.http.routers.portainer.tls.certresolver=letsencrypt"
      - "traefik.http.services.portainer.loadbalancer.server.port=9000"
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # Monitoring & Auto-updates
  watchtower:
    image: containrrr/watchtower
    container_name: watchtower
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_SCHEDULE=0 0 4 * * *  # 4 AM daily
      - WATCHTOWER_NOTIFICATIONS=slack
      - WATCHTOWER_NOTIFICATION_SLACK_HOOK_URL=${SLACK_WEBHOOK_URL}
    deploy:
      resources:
        limits:
          memory: 64M
          cpus: '0.1'

volumes:
  traefik-data:
  api-data:
  api-logs:
  uploader-temp:
  portainer-data:

networks:
  default:
    name: yendorcats-production
```

## Server Setup Script

### #automation #setup #server #security

```bash
#!/bin/bash
# server-setup.sh - Complete server preparation script

set -euo pipefail

# Configuration
DOMAIN="${1:-yourdomain.com}"
USER="${2:-yendor}"
SSH_KEY="${3:-}"

log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# Update system
log "Updating system packages..."
apt update && apt upgrade -y

# Install essential packages
log "Installing essential packages..."
apt install -y curl wget git ufw fail2ban htop ncdu tree jq

# Create application user
log "Creating application user..."
if ! id "$USER" &>/dev/null; then
    useradd -m -s /bin/bash "$USER"
    usermod -aG sudo "$USER"
fi

# Configure SSH security
log "Configuring SSH security..."
sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
systemctl restart ssh

# Setup SSH key if provided
if [[ -n "$SSH_KEY" ]]; then
    log "Setting up SSH key..."
    mkdir -p "/home/<USER>/.ssh"
    echo "$SSH_KEY" > "/home/<USER>/.ssh/authorized_keys"
    chown -R "$USER:$USER" "/home/<USER>/.ssh"
    chmod 700 "/home/<USER>/.ssh"
    chmod 600 "/home/<USER>/.ssh/authorized_keys"
fi

# Configure firewall
log "Configuring firewall..."
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw --force enable

# Install Docker
log "Installing Docker..."
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
usermod -aG docker "$USER"
systemctl enable docker
systemctl start docker

# Install Docker Compose
log "Installing Docker Compose..."
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Create application directories
log "Creating application directories..."
mkdir -p "/opt/yendorcats"
mkdir -p "/opt/backups"
chown -R "$USER:$USER" "/opt/yendorcats"
chown -R "$USER:$USER" "/opt/backups"

# Configure log rotation
log "Configuring log rotation..."
cat > /etc/logrotate.d/yendorcats << EOF
/opt/yendorcats/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 $USER $USER
}
EOF

# Setup automatic security updates
log "Configuring automatic security updates..."
apt install -y unattended-upgrades
echo 'Unattended-Upgrade::Automatic-Reboot "false";' >> /etc/apt/apt.conf.d/50unattended-upgrades

log "Server setup completed!"
log "Next steps:"
log "1. Login as user '$USER'"
log "2. Clone your repository to /opt/yendorcats"
log "3. Configure environment variables"
log "4. Run deployment script"
```

## Scaling Strategy

### #scaling #growth #docker #swarm #kubernetes

**Phase 1: Single Server (0-1000 users)**
- Current setup with resource limits
- Vertical scaling (upgrade VPS)
- Cost: $7-15/month

**Phase 2: Docker Swarm (1000-10000 users)**
- 3-node cluster for high availability
- Shared storage with GlusterFS
- Load balancing with Traefik
- Cost: $25-50/month

**Phase 3: Kubernetes (10000+ users)**
- k3s lightweight Kubernetes
- Auto-scaling based on metrics
- Managed database services
- Cost: $100-300/month

## Cost Optimization Tips

### #optimization #cost #resources #monitoring

1. **Resource Monitoring**
   - Use Portainer for container resource usage
   - Set up alerts for high resource usage
   - Regular cleanup of unused images/volumes

2. **Storage Optimization**
   - Use Backblaze B2 for file storage ($5/TB/month)
   - Implement image compression
   - Regular cleanup of old backups

3. **Network Optimization**
   - Use CDN for static assets (Cloudflare free tier)
   - Implement caching strategies
   - Optimize API responses

4. **Database Optimization**
   - SQLite for small to medium datasets
   - Regular database maintenance
   - Implement connection pooling

---

**Next Steps:** [[Production Deployment Checklist]] | [[Security Hardening Guide]] | [[Monitoring Setup]]
