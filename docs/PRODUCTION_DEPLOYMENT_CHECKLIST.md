---
title: "YendorCats Production Deployment Checklist"
tags: [deployment, production, docker, infrastructure, security, automation]
created: 2025-08-03
updated: 2025-08-03
---

# YendorCats Production Deployment Checklist

## Pre-Deployment Security & Configuration

### #security #environment #vault #secrets
- [ ] **Secrets Management**
  - [ ] Hashi<PERSON><PERSON><PERSON> Vault configured and secured
  - [ ] JWT secrets generated (32+ characters minimum)
  - [ ] Database encryption keys rotated
  - [ ] S3/B2 storage credentials validated and restricted
  - [ ] Admin credentials configured with strong passwords
  - [ ] All environment variables secured in Vault

- [ ] **SSL/TLS & Network Security**
  - [ ] Domain name configured with proper DNS
  - [ ] SSL certificates obtained (Let's Encrypt recommended)
  - [ ] HTTPS redirect enforced
  - [ ] Security headers implemented (HSTS, CSP, etc.)
  - [ ] Firewall rules configured (ports 80, 443, 22 only)
  - [ ] SSH key-based authentication enabled
  - [ ] Root login disabled

### #database #backup #persistence
- [ ] **Database & Storage**
  - [ ] SQLite database backup strategy implemented
  - [ ] Automated daily backups configured
  - [ ] Data persistence volumes configured
  - [ ] Database migration scripts tested
  - [ ] Backblaze B2 bucket configured with proper permissions
  - [ ] File upload size limits configured
  - [ ] Storage cleanup policies implemented

## Infrastructure Setup

### #budget #vps #hosting #providers
**Recommended Budget-Friendly VPS Providers:**

| Provider | Plan | Specs | Monthly Cost | Best For |
|----------|------|-------|--------------|----------|
| **Hetzner** | CPX21 | 3 vCPU, 4GB RAM, 80GB SSD | ~$7 | Best value |
| **Vultr** | Regular | 2 vCPU, 4GB RAM, 80GB SSD | ~$12 | Global reach |
| **OVH** | VPS SSD 2 | 2 vCPU, 4GB RAM, 80GB SSD | ~$10 | EU compliance |
| **DigitalOcean** | Basic | 2 vCPU, 4GB RAM, 80GB SSD | ~$24 | Ease of use |

### #docker #portainer #management
**Container Management Setup:**

```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Portainer for GUI management
docker volume create portainer_data
docker run -d -p 8000:8000 -p 9443:9443 \
  --name portainer --restart=always \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v portainer_data:/data \
  portainer/portainer-ce:latest
```

## Production Architecture

### #architecture #traefik #reverseproxy #ssl
**Recommended Single-Server Setup with Traefik:**

```yaml
# docker-compose.production.yml
version: '3.8'

services:
  # Reverse Proxy & SSL Termination
  traefik:
    image: traefik:v2.10
    container_name: traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik.yml:/etc/traefik/traefik.yml
      - ./acme.json:/acme.json
    environment:
      - TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_EMAIL=<EMAIL>

  # YendorCats API
  api:
    image: yendorcats/api:latest
    container_name: yendorcats-api
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
    volumes:
      - api-data:/app/data
      - api-logs:/app/logs
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api.rule=Host(`api.yourdomain.com`)"
      - "traefik.http.routers.api.tls.certresolver=letsencrypt"
      - "traefik.http.services.api.loadbalancer.server.port=80"

  # Frontend
  frontend:
    image: nginx:alpine
    container_name: yendorcats-frontend
    restart: unless-stopped
    volumes:
      - ./frontend:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`yourdomain.com`)"
      - "traefik.http.routers.frontend.tls.certresolver=letsencrypt"

volumes:
  api-data:
  api-logs:
```

## Deployment Automation

### #automation #scripts #cicd
- [ ] **Automated Deployment Script**
  - [ ] Build and push images to registry
  - [ ] Deploy with zero-downtime updates
  - [ ] Health checks and rollback capability
  - [ ] Automated testing post-deployment

- [ ] **Monitoring & Alerting**
  - [ ] Container health monitoring
  - [ ] Resource usage alerts
  - [ ] Application performance monitoring
  - [ ] Log aggregation and analysis

## Scaling Strategy

### #scaling #clustering #growth
**Growth Path:**

1. **Phase 1: Single Server** (Current)
   - Single VPS with Docker Compose
   - Portainer for management
   - Traefik for load balancing

2. **Phase 2: Horizontal Scaling**
   - Docker Swarm cluster (3+ nodes)
   - Shared storage (NFS or GlusterFS)
   - Database clustering or managed service

3. **Phase 3: Kubernetes**
   - k3s for lightweight Kubernetes
   - Helm charts for deployment
   - Auto-scaling based on metrics

## Security Hardening

### #security #hardening #monitoring
- [ ] **Server Security**
  - [ ] Regular security updates automated
  - [ ] Fail2ban configured for SSH protection
  - [ ] UFW firewall configured
  - [ ] Log monitoring and alerting
  - [ ] Regular security audits scheduled

- [ ] **Application Security**
  - [ ] Container images scanned for vulnerabilities
  - [ ] Secrets rotation automated
  - [ ] API rate limiting implemented
  - [ ] Input validation and sanitization verified

## Backup & Recovery

### #backup #recovery #disaster
- [ ] **Backup Strategy**
  - [ ] Database backups (daily, weekly, monthly retention)
  - [ ] Configuration backups
  - [ ] Image and file backups to B2
  - [ ] Backup verification and testing

- [ ] **Disaster Recovery**
  - [ ] Recovery procedures documented
  - [ ] Recovery time objectives defined
  - [ ] Regular disaster recovery testing
  - [ ] Infrastructure as Code for quick rebuilds

---

## Quick Deployment Commands

```bash
# 1. Server preparation
sudo apt update && sudo apt upgrade -y
curl -fsSL https://get.docker.com -o get-docker.sh && sudo sh get-docker.sh

# 2. Clone and configure
git clone your-repo.git /opt/yendorcats
cd /opt/yendorcats
cp .env.production.template .env.production

# 3. Deploy
./deploy-production.sh

# 4. Verify
curl https://yourdomain.com/health
```

---

**Next Steps:** [[Production Monitoring Setup]] | [[Scaling Strategy Guide]] | [[Security Hardening Checklist]]
