---

# YendorCats Cloudflare Rapid Deployment Guide

## Overview

Quick setup guide for deploying YendorCats with Cloudflare CDN for immediate HTTPS and performance optimization.

## Tags
#yendorcats #cloudflare #deployment #https #cdn #performance #rapid

---

## 🚀 Rapid Deployment Steps (15 minutes)

### 1. Cloudflare DNS Setup

1. **Add your domain to Cloudflare**
2. **Update nameservers** at your domain registrar
3. **Add DNS records**:
   ```
   Type: A
   Name: @ (or your subdomain)
   Content: YOUR_SERVER_IP
   Proxy status: Proxied (orange cloud)
   ```

### 2. Cloudflare SSL/TLS Settings

**Navigate to SSL/TLS tab:**

1. **Overview**: Set to "Flexible" (for rapid deployment)
   - ✅ Users → Cloudflare: Encrypted
   - ⚠️ Cloudflare → Origin: HTTP (upgrade later)

2. **Edge Certificates**: 
   - ✅ Always Use HTTPS: ON
   - ✅ HTTP Strict Transport Security: Enable
   - ✅ Minimum TLS Version: 1.2

### 3. Cloudflare Caching Rules

**Navigate to Caching → Configuration:**

1. **Browser Cache TTL**: 4 hours
2. **Caching Level**: Standard

**Navigate to Rules → Page Rules (or Cache Rules):**

Create these rules in order:

#### Rule 1: Static Assets (Highest Priority)
```
URL Pattern: *yourdomain.com/*.css
           *yourdomain.com/*.js
           *yourdomain.com/*.png
           *yourdomain.com/*.jpg
           *yourdomain.com/*.jpeg
           *yourdomain.com/*.gif
           *yourdomain.com/*.ico
           *yourdomain.com/*.svg
           *yourdomain.com/*.woff*
           *yourdomain.com/*.ttf
           *yourdomain.com/*.eot

Settings:
- Cache Level: Cache Everything
- Edge Cache TTL: 1 year
- Browser Cache TTL: 1 year
```

#### Rule 2: API Bypass
```
URL Pattern: *yourdomain.com/api/*

Settings:
- Cache Level: Bypass
```

#### Rule 3: HTML Files
```
URL Pattern: *yourdomain.com/*.html
           *yourdomain.com/

Settings:
- Cache Level: Cache Everything
- Edge Cache TTL: 2 hours
- Browser Cache TTL: 1 hour
```

### 4. Cloudflare Performance Settings

**Navigate to Speed → Optimization:**

1. **Auto Minify**: 
   - ✅ JavaScript
   - ✅ CSS  
   - ✅ HTML

2. **Brotli**: ✅ Enable

3. **Early Hints**: ✅ Enable

**Navigate to Speed → Image Optimization:**
- ✅ Polish: Lossless (for gallery quality)
- ✅ WebP conversion: Enable

### 5. Cloudflare Security (Optional but Recommended)

**Navigate to Security → Settings:**
- Security Level: Medium
- Challenge Passage: 30 minutes

## 🔧 Backend Configuration for Backblaze B2

Since your images are on Backblaze B2, ensure your API returns proper cache headers:

### ASP.NET Core Headers (Add to your API)

```csharp
// In your image/gallery endpoints
Response.Headers.Add("Cache-Control", "public, max-age=31536000"); // 1 year
Response.Headers.Add("Vary", "Accept-Encoding");
Response.Headers.Add("ETag", imageETag); // Use file hash or timestamp
```

### Backblaze B2 Direct URLs

If serving images directly from B2, configure these headers in your B2 bucket:
```json
{
  "cache-control": "public, max-age=31536000",
  "vary": "Accept-Encoding"
}
```

## 📊 Performance Monitoring

### Cloudflare Analytics
- Monitor cache hit ratio (aim for >90% for static assets)
- Check bandwidth savings
- Monitor response times

### Key Metrics to Watch
- **Cache Hit Ratio**: Should be >90% for images/static files
- **Origin Response Time**: Should be <500ms
- **Edge Response Time**: Should be <100ms globally

## 🚨 Post-Launch Optimizations (Do Later)

### 1. Upgrade to Full SSL
```bash
# Generate Let's Encrypt certificate on origin
certbot --nginx -d yourdomain.com
```
Then change Cloudflare SSL to "Full (Strict)"

### 2. Advanced Caching
- Implement Cache API in your frontend
- Add service worker for offline support
- Use Cloudflare Workers for advanced logic

### 3. Image Optimization
- Implement responsive images
- Use Cloudflare Image Resizing
- Consider WebP/AVIF formats

## 🔍 Troubleshooting

### Common Issues

1. **502 Bad Gateway**: Check if backend is running on port 8080
2. **SSL Errors**: Ensure Flexible SSL mode is selected
3. **Images not loading**: Check CORS headers and B2 bucket permissions
4. **Slow API responses**: Verify API caching is bypassed

### Quick Checks
```bash
# Test origin server
curl -H "Host: yourdomain.com" http://YOUR_SERVER_IP/health

# Test through Cloudflare
curl -I https://yourdomain.com/health
```

---

## ⚡ Deploy Command

```bash
# Deploy the updated configuration
docker-compose -f docker-compose.production.yml up -d --build

# Verify deployment
docker-compose -f docker-compose.production.yml ps
docker-compose -f docker-compose.production.yml logs -f
```

**Expected Result**: Your gallery website will be live with HTTPS, optimized caching, and global CDN distribution within 15 minutes of DNS propagation.

---
