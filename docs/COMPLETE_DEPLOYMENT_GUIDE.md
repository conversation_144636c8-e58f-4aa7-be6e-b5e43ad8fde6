---
title: "Complete YendorCats Deployment Guide"
tags: [deployment, production, guide, infrastructure, automation, security]
created: 2025-08-03
updated: 2025-08-03
---

# Complete YendorCats Deployment Guide

## Quick Start (5 Minutes)

### #quickstart #deployment #automation

```bash
# 1. Get a VPS (Hetzner CPX21 recommended - €6.90/month)
# 2. Run server setup
curl -sSL https://raw.githubusercontent.com/your-repo/yendorcats/main/scripts/server-setup.sh | sudo bash -s yourdomain.com yendor

# 3. <PERSON><PERSON> as application user
ssh yendor@your-server-ip

# 4. Clone and configure
git clone https://github.com/your-repo/yendorcats.git /opt/yendorcats
cd /opt/yendorcats
cp .env.production.template .env.production
nano .env.production  # Configure your settings

# 5. Deploy
chmod +x deploy-production.sh
./deploy-production.sh

# 6. Access your application
# Frontend: https://yourdomain.com
# API: https://api.yourdomain.com
# Admin: https://portainer.yourdomain.com
```

## Detailed Setup Process

### Step 1: VPS Selection & Setup

#### #vps #providers #budget
**Recommended Provider: Hetzner**
- Plan: CPX21 (3 vCPU, 4GB RAM, 80GB SSD)
- Cost: €6.90/month (~$7.50)
- Location: Choose closest to your users

**Server Setup:**
```bash
# Run as root on fresh Ubuntu 22.04 server
curl -sSL https://raw.githubusercontent.com/your-repo/yendorcats/main/scripts/server-setup.sh | bash -s yourdomain.com yendor "your-ssh-public-key"
```

### Step 2: DNS Configuration

#### #dns #domain #cloudflare
Configure these DNS records:
```
A     yourdomain.com          -> your-server-ip
A     www.yourdomain.com      -> your-server-ip
A     api.yourdomain.com      -> your-server-ip
A     upload.yourdomain.com   -> your-server-ip
A     portainer.yourdomain.com -> your-server-ip
A     traefik.yourdomain.com  -> your-server-ip
```

### Step 3: Environment Configuration

#### #environment #secrets #configuration
```bash
# Copy and configure environment
cp .env.production.template .env.production

# Generate JWT secret (32+ characters)
openssl rand -base64 32

# Generate Traefik auth hash
htpasswd -nb admin your-password

# Configure Backblaze B2
# - Create bucket
# - Generate application key
# - Set CORS policy
```

### Step 4: SSL Certificate Setup

#### #ssl #letsencrypt #traefik
Traefik automatically handles SSL certificates via Let's Encrypt:
- Certificates are automatically requested and renewed
- HTTPS redirect is enforced
- Security headers are applied

### Step 5: Deployment

#### #deployment #automation #docker
```bash
# Make deployment script executable
chmod +x deploy-production.sh

# Run deployment
./deploy-production.sh

# Check status
docker-compose -f docker-compose.production.yml ps

# View logs
docker-compose -f docker-compose.production.yml logs -f
```

## Architecture Overview

### #architecture #containers #services

```mermaid
graph TB
    Internet[Internet] --> Traefik[Traefik Reverse Proxy]
    
    Traefik --> Frontend[Frontend - Nginx]
    Traefik --> API[API - .NET Core]
    Traefik --> Uploader[File Uploader]
    Traefik --> Portainer[Portainer Dashboard]
    
    API --> Database[(SQLite Database)]
    API --> B2[Backblaze B2 Storage]
    Uploader --> B2
    
    Watchtower[Watchtower] --> API
    Watchtower --> Frontend
    Watchtower --> Uploader
    
    subgraph "Docker Volumes"
        APIData[API Data]
        APILogs[API Logs]
        PortainerData[Portainer Data]
        TraefikData[Traefik Data]
    end
    
    API --> APIData
    API --> APILogs
    Portainer --> PortainerData
    Traefik --> TraefikData
```

## Service Endpoints

### #endpoints #services #access

| Service | URL | Purpose | Authentication |
|---------|-----|---------|----------------|
| **Frontend** | https://yourdomain.com | Main application | Public |
| **API** | https://api.yourdomain.com | REST API | JWT Token |
| **Uploader** | https://upload.yourdomain.com | File uploads | API Key |
| **Portainer** | https://portainer.yourdomain.com | Container management | Basic Auth |
| **Traefik** | https://traefik.yourdomain.com | Proxy dashboard | Basic Auth |

## Monitoring & Maintenance

### #monitoring #maintenance #automation

**Automated Tasks:**
- **Watchtower**: Updates containers daily at 4 AM
- **Log Rotation**: Rotates logs daily, keeps 30 days
- **Backups**: Database backed up before deployments
- **SSL Renewal**: Automatic via Let's Encrypt

**Manual Monitoring:**
```bash
# Check container status
docker-compose -f docker-compose.production.yml ps

# View resource usage
docker stats

# Check logs
docker-compose -f docker-compose.production.yml logs -f api

# Check disk usage
df -h
du -sh /opt/yendorcats/*
```

## Scaling Strategy

### #scaling #growth #performance

**Current Capacity (Single Server):**
- **Users**: 1,000-5,000 concurrent
- **Storage**: Unlimited (Backblaze B2)
- **Bandwidth**: 20TB/month (Hetzner)

**Scaling Options:**

1. **Vertical Scaling** (Immediate)
   - Upgrade to CPX31: 4 vCPU, 8GB RAM (+€7/month)
   - Upgrade to CPX41: 8 vCPU, 16GB RAM (+€20/month)

2. **Horizontal Scaling** (Future)
   - Docker Swarm cluster (3+ nodes)
   - Load balancer with multiple API instances
   - Shared storage with GlusterFS

3. **Managed Services** (Growth Phase)
   - Managed PostgreSQL database
   - CDN for static assets
   - Container orchestration (Kubernetes)

## Troubleshooting

### #troubleshooting #debugging #support

**Common Issues:**

1. **SSL Certificate Issues**
   ```bash
   # Check certificate status
   docker-compose logs traefik | grep -i acme
   
   # Force certificate renewal
   docker-compose restart traefik
   ```

2. **Database Connection Issues**
   ```bash
   # Check database file permissions
   ls -la /opt/yendorcats/data/
   
   # Check API logs
   docker-compose logs api | grep -i database
   ```

3. **High Resource Usage**
   ```bash
   # Check container resources
   docker stats
   
   # Restart resource-heavy containers
   docker-compose restart api
   ```

## Security Checklist

### #security #hardening #checklist

- [ ] **Server Security**
  - [ ] SSH key-based authentication only
  - [ ] Firewall configured (UFW)
  - [ ] Fail2ban installed and configured
  - [ ] Regular security updates enabled

- [ ] **Application Security**
  - [ ] Strong JWT secrets (32+ characters)
  - [ ] HTTPS enforced everywhere
  - [ ] Security headers configured
  - [ ] Rate limiting enabled
  - [ ] Input validation implemented

- [ ] **Container Security**
  - [ ] Non-root containers where possible
  - [ ] Resource limits configured
  - [ ] Regular image updates (Watchtower)
  - [ ] Secrets not in environment variables

## Backup & Recovery

### #backup #recovery #disaster

**Automated Backups:**
- Database: Before each deployment
- Configuration: Included in deployment backups
- Retention: 7 days locally, 30 days in B2

**Manual Backup:**
```bash
# Create manual backup
./deploy-production.sh backup

# List backups
ls -la /opt/backups/yendorcats/

# Restore from backup
./deploy-production.sh rollback
```

**Disaster Recovery:**
1. Provision new server
2. Run server setup script
3. Restore latest backup
4. Update DNS records
5. Verify functionality

---

## Cost Breakdown

### #cost #budget #monthly

| Component | Monthly Cost | Notes |
|-----------|--------------|-------|
| **VPS (Hetzner CPX21)** | €6.90 | 3 vCPU, 4GB RAM, 80GB SSD |
| **Domain** | $1-2 | .com domain |
| **Backblaze B2** | $5-10 | 1TB storage + bandwidth |
| **SSL Certificate** | $0 | Let's Encrypt (free) |
| **Monitoring** | $0 | Self-hosted |
| **Total** | **~$15/month** | For 1,000+ users |

**Scaling Costs:**
- 5,000 users: ~$25/month (upgrade VPS)
- 10,000 users: ~$50/month (multi-server)
- 50,000 users: ~$200/month (managed services)

---

**Next Steps:** [[Production Monitoring Setup]] | [[Security Hardening Guide]] | [[Performance Optimization]]
