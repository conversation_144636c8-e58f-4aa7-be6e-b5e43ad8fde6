#!/bin/bash

# Production Diagnosis Script for YendorCats Gallery Issue
# Run this on your EC2 instance to diagnose the B2 credential issue

echo "🔍 YendorCats Production Diagnosis"
echo "=================================="

# Check if running on production server
if [ ! -f "docker-compose.production.yml" ]; then
    echo "❌ This script should be run on your production server where docker-compose.production.yml exists"
    exit 1
fi

echo "📋 Checking Docker containers..."
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo ""

echo "🔍 Checking API container logs for credential errors..."
echo "Looking for 'Malformed Access Key' errors:"
docker logs yendorcats-api-production 2>&1 | grep -i "malformed\|access.*key" | tail -5
echo ""

echo "🔍 Checking S3 configuration in API logs..."
echo "S3 initialization logs:"
docker logs yendorcats-api-production 2>&1 | grep -i "s3storageservice initialized" | tail -3
echo ""

echo "🔍 Checking metadata sync errors..."
echo "Recent sync errors:"
docker logs yendorcats-api-production 2>&1 | grep -i "error.*sync\|error.*listing" | tail -5
echo ""

echo "🧪 Testing API endpoints..."
echo "Testing gallery endpoint:"
response=$(curl -s -w "HTTP_%{http_code}" "http://localhost:5003/api/PublicGallery/category/kittens")
http_code=$(echo "$response" | grep -o "HTTP_[0-9]*" | cut -d'_' -f2)
content=$(echo "$response" | sed 's/HTTP_[0-9]*$//')

if [ "$http_code" = "200" ]; then
    echo "✅ API responding ($http_code)"
    image_count=$(echo "$content" | jq -r '.images | length' 2>/dev/null || echo "unknown")
    echo "   Found $image_count images"
    
    if [ "$image_count" = "0" ]; then
        echo "❌ No images found - this confirms the B2 sync issue"
    fi
else
    echo "❌ API not responding ($http_code)"
fi
echo ""

echo "🔍 Checking environment variables..."
echo "Checking if B2 credentials are set (without revealing values):"

# Check if running containers have the environment variables
docker exec yendorcats-api-production printenv | grep -E "AWS_S3_ACCESS_KEY|AWS_S3_SECRET_KEY|B2_APPLICATION_KEY" | sed 's/=.*/=***HIDDEN***/'
echo ""

echo "📝 Diagnosis Summary:"
echo "===================="
echo "1. Check the logs above for 'Malformed Access Key Id' errors"
echo "2. If you see these errors, your B2 credentials are invalid or missing"
echo "3. The prefix 'YendorCats-General-SiteAccess/' is correctly configured"
echo "4. The issue is NOT CORS or frontend - it's backend B2 authentication"
echo ""
echo "🔧 To fix this issue:"
echo "1. Update your .env.production file with correct B2 credentials"
echo "2. Restart the API container: docker-compose -f docker-compose.production.yml restart api"
echo "3. Monitor logs: docker logs yendorcats-api-production -f"
echo ""
echo "💡 Need to check your B2 credentials? Log into Backblaze B2 console:"
echo "   https://secure.backblaze.com/b2_buckets.htm"
