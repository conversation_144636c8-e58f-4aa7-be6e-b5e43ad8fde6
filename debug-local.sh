#!/bin/bash

# YendorCats Local Debug Script
# Builds and runs the application locally for troubleshooting

set -e

echo "🔧 YendorCats Local Debug Environment Setup"
echo "==========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if .env.local-debug exists
if [ ! -f ".env.local-debug" ]; then
    print_warning ".env.local-debug not found. Please create it with your credentials."
    print_status "You can copy from .env.local-debug template and fill in your AWS/B2 credentials."
    exit 1
fi

print_status "Loading environment from .env.local-debug"
export $(cat .env.local-debug | grep -v '^#' | xargs)

# Clean up any existing containers
print_status "Cleaning up existing debug containers..."
docker-compose -f docker-compose.local-debug.yml down --remove-orphans 2>/dev/null || true

# Remove old images to force rebuild
print_status "Removing old debug images..."
docker rmi yendorcats-api:local-debug yendorcats-frontend:local-debug yendorcats-uploader:local-debug 2>/dev/null || true

# Build the images
print_status "Building API image..."
docker-compose -f docker-compose.local-debug.yml build api

print_status "Building Frontend image..."
docker-compose -f docker-compose.local-debug.yml build frontend

print_status "Building Uploader image..."
docker-compose -f docker-compose.local-debug.yml build uploader

# Start the services
print_status "Starting debug services..."
docker-compose -f docker-compose.local-debug.yml up -d

# Wait for services to be healthy
print_status "Waiting for services to start..."
sleep 10

# Check service health
print_status "Checking service health..."

# Check API health
if curl -f http://localhost:5003/health > /dev/null 2>&1; then
    print_success "API is healthy at http://localhost:5003"
else
    print_error "API health check failed"
fi

# Check Frontend health
if curl -f http://localhost:8080/health > /dev/null 2>&1; then
    print_success "Frontend is healthy at http://localhost:8080"
else
    print_error "Frontend health check failed"
fi

# Check Uploader health
if curl -f http://localhost:5002/health > /dev/null 2>&1; then
    print_success "Uploader is healthy at http://localhost:5002"
else
    print_error "Uploader health check failed"
fi

echo ""
print_success "🎉 Debug environment is ready!"
echo ""
echo "📋 Service URLs:"
echo "   Frontend: http://localhost:8080"
echo "   API:      http://localhost:5003"
echo "   Uploader: http://localhost:5002"
echo "   Database: localhost:3306"
echo ""
echo "🔍 Debug Commands:"
echo "   View logs:     docker-compose -f docker-compose.local-debug.yml logs -f"
echo "   View API logs: docker logs yendorcats-api-debug -f"
echo "   Stop services: docker-compose -f docker-compose.local-debug.yml down"
echo ""
echo "🧪 Test API Endpoints:"
echo "   curl -H 'Origin: http://localhost:8080' http://localhost:5003/api/PublicGallery/category/kittens"
echo "   curl -H 'Origin: http://localhost:8080' http://localhost:5003/api/Sync/metadata/status"
echo ""

# Test CORS
print_status "Testing CORS configuration..."
echo ""

# Test API endpoint with CORS headers
echo "Testing API with CORS headers:"
response=$(curl -s -H "Origin: http://localhost:8080" -H "Access-Control-Request-Method: GET" -H "Access-Control-Request-Headers: Content-Type" -X OPTIONS http://localhost:5003/api/PublicGallery/category/kittens -w "HTTP_%{http_code}")
http_code=$(echo "$response" | grep -o "HTTP_[0-9]*" | cut -d'_' -f2)

if [ "$http_code" = "200" ] || [ "$http_code" = "204" ]; then
    print_success "CORS preflight check passed ($http_code)"
else
    print_error "CORS preflight check failed ($http_code)"
fi

# Test actual API call
echo "Testing actual API call:"
response=$(curl -s -H "Origin: http://localhost:8080" http://localhost:5003/api/PublicGallery/category/kittens -w "HTTP_%{http_code}")
http_code=$(echo "$response" | grep -o "HTTP_[0-9]*" | cut -d'_' -f2)

if [ "$http_code" = "200" ]; then
    print_success "API call successful ($http_code)"
    content=$(echo "$response" | sed 's/HTTP_[0-9]*$//')
    image_count=$(echo "$content" | jq -r '.images | length' 2>/dev/null || echo "unknown")
    echo "   Found $image_count images"
else
    print_error "API call failed ($http_code)"
    content=$(echo "$response" | sed 's/HTTP_[0-9]*$//')
    echo "   Response: $content"
fi

echo ""
print_status "🔍 Open http://localhost:8080 in your browser to test the frontend"
print_status "📊 Check browser developer tools Network tab for CORS errors"
